#!/bin/bash

# Simple bash script to trigger graceful shutdown of GPS parser
# This creates a shutdown signal file that the parser monitors

BASE_PATH="/home/<USER>/ControlOne/public_html/public/"
SHUTDOWN_FILE="${BASE_PATH}shutdown_signal.txt"

echo "🛑 Triggering graceful shutdown of GPS parser..."
echo "📊 This will ensure all GPS data is processed and saved before shutdown."

# Create shutdown signal file
echo "Shutdown requested at: $(date)" > "$SHUTDOWN_FILE"

if [ $? -eq 0 ]; then
    echo "✅ Shutdown signal created successfully!"
    echo "⏳ The parser will detect this signal within 5 seconds and begin graceful shutdown."
    echo "📋 Check your tmux session to see the shutdown progress."
    echo ""
    echo "The graceful shutdown will:"
    echo "  ✅ Process all remaining GPS data in queues"
    echo "  ✅ Save all data to JSON files"
    echo "  ✅ Close connections properly"
    echo "  ✅ Ensure no data loss"
else
    echo "❌ Failed to create shutdown signal file."
    echo "   Please check file permissions and try again."
fi
