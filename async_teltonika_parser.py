#!/usr/bin/env python3
"""
Async Teltonika Parser
----------------------
An optimized parser for Teltonika GPS devices using asyncio architecture.
Separates TCP server from data processing for improved performance.

Features:
- Non-blocking I/O with asyncio
- Separate TCP server and worker processes
- Optimized JSON dumping for live and historical data
- Duplicate detection considering GPS coordinates
- Efficient handling of bulk device data
"""

import asyncio
import json
import os
import time
import datetime
import pytz
import hashlib
import logging
from collections import defaultdict
import aiofiles
import aiofiles.os
from typing import Dict, List, Tuple, Any, Optional, Set

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("async_teltonika")

# Constants
BASE_PATH = "/home/<USER>/ControlOne/public_html/public/test/"
COMMAND_QUEUE_PATH = BASE_PATH + "command/queue.json"
PLATFORM_BASE_URL = "https://piattaforma.controllone.it/"
LARAVEL_COMMAND_API_URL = PLATFORM_BASE_URL + "api/save-command-response"

# Constants for data storage
DATA_PATH = BASE_PATH + "data"
HISTORY_PATH = DATA_PATH + "/history"
LIVE_DATA_FILE = "live.json"

# Constants for queue management
MAX_QUEUE_SIZE = 1000  # Prevent unbounded growth
MAX_PROCESSING_DELAY = 50  # Maximum seconds to wait for ordering
CLEANUP_INTERVAL = 600  # Cleanup old queues every 10 minutes
MAX_WORKERS = 4  # Number of worker processes for parsing

# In-memory caches
live_data_cache = {}
last_write_time = 0
write_interval = 1.0  # Seconds between writes to reduce I/O

# Cache for duplicate detection
processed_packets = set()
MAX_CACHE_SIZE = 10000

# Locks for file access (will be initialized in main)
live_data_lock = None

class TeltonikaParser:
    """Main parser class for Teltonika devices"""

    def __init__(self):
        """Initialize the parser with required data structures"""
        self.device_queues = defaultdict(asyncio.Queue)
        self.last_activity = defaultdict(float)
        # Using default thread pool instead of process pool to avoid pickling issues

    async def start_server(self, host: str = '0.0.0.0', port: int = 5001):
        """Start the TCP server to listen for device connections"""
        server = await asyncio.start_server(
            self.handle_client, host, port
        )

        addr = server.sockets[0].getsockname()
        logger.info(f'Serving on {addr}')

        # Start the cleanup task
        asyncio.create_task(self._cleanup_old_queues())

        async with server:
            await server.serve_forever()

    async def _cleanup_old_queues(self):
        """Periodically clean up inactive device queues"""
        while True:
            current_time = time.time()
            for imei in list(self.last_activity.keys()):
                if current_time - self.last_activity[imei] > CLEANUP_INTERVAL:
                    del self.device_queues[imei]
                    del self.last_activity[imei]
            await asyncio.sleep(CLEANUP_INTERVAL)

    async def handle_client(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """Handle a client connection"""
        addr = writer.get_extra_info('peername')
        logger.info(f"Connected by {addr}")

        device_imei = "default_IMEI"

        try:
            while True:
                # Set a timeout for reading data
                try:
                    data = await asyncio.wait_for(reader.read(10240), timeout=5)
                except asyncio.TimeoutError:
                    logger.warning(f"Timeout waiting for data from {addr}")
                    break

                if not data:
                    break

                # Check if this is an IMEI identification packet
                if await self._is_imei_packet(data):
                    device_imei = await self._extract_imei(data)
                    writer.write((1).to_bytes(1, byteorder="big"))
                    await writer.drain()
                    logger.info(f"IMEI received: {device_imei}")
                    continue

                # Check if this is a valid codec 8E packet
                if await self._is_valid_codec_packet(data):
                    # Queue the message for processing
                    await self._queue_message(device_imei, data, writer)

                    # Process any pending commands (to be implemented later)
                    # await self._process_commands(device_imei, writer)

            logger.info(f"Connection closed for {addr}")
            writer.close()
            await writer.wait_closed()

        except Exception as e:
            logger.error(f"Error handling client {addr}: {e}")
            writer.close()
            try:
                await writer.wait_closed()
            except:
                pass

    async def _is_imei_packet(self, data: bytes) -> bool:
        """Check if the packet is an IMEI identification packet"""
        try:
            hex_data = data.hex()
            if len(hex_data) < 4:
                return False

            # IMEI packets typically start with 000F
            if hex_data[:4] != "000f":
                return False

            # Try to decode the IMEI
            ascii_imei = bytes.fromhex(hex_data[4:]).decode()
            return len(ascii_imei) == 15 and ascii_imei.isdigit()
        except Exception as e:
            logger.error(f"Error checking IMEI packet: {e}")
            return False

    async def _extract_imei(self, data: bytes) -> str:
        """Extract the IMEI from an identification packet"""
        try:
            hex_data = data.hex()
            ascii_imei = bytes.fromhex(hex_data[4:]).decode()
            return ascii_imei
        except Exception as e:
            logger.error(f"Error extracting IMEI: {e}")
            return "default_IMEI"

    async def _is_valid_codec_packet(self, data: bytes) -> bool:
        """Check if the packet is a valid codec 8E packet"""
        try:
            hex_data = data.hex()

            # Basic length check
            if len(hex_data) < 20:
                return False

            # Check codec type (8E or 08)
            codec_type = hex_data[16:18].upper()
            if codec_type != "8E" and codec_type != "08":
                return False

            # Perform CRC check (to be implemented)
            # return await self._check_crc(hex_data)
            return True  # Placeholder, will implement CRC check later

        except Exception as e:
            logger.error(f"Error validating codec packet: {e}")
            return False

    async def _queue_message(self, device_imei: str, data: bytes, writer: asyncio.StreamWriter):
        """Queue a message for processing"""
        try:
            # Extract timestamp for priority queue ordering
            timestamp = await self._extract_timestamp(data)

            # Update last activity time
            self.last_activity[device_imei] = time.time()

            # Add to the device's queue
            await self.device_queues[device_imei].put((timestamp, data, writer))

            # Start processing if not already running
            if not hasattr(self, f"processing_{device_imei}") or not getattr(self, f"processing_{device_imei}"):
                setattr(self, f"processing_{device_imei}", True)
                asyncio.create_task(self._process_device_queue(device_imei))

        except Exception as e:
            logger.error(f"Error queueing message: {e}")

    async def _extract_timestamp(self, data: bytes) -> int:
        """Extract timestamp from the data packet for ordering"""
        try:
            hex_data = data.hex()

            # Check codec type to determine correct position
            codec_type = hex_data[16:18].upper() if len(hex_data) >= 18 else ""

            # Adjust position based on codec type
            if codec_type == "8E" or codec_type == "08":
                timestamp_hex = hex_data[20:36]  # Standard position for these codecs
            else:
                # Fallback mechanism
                timestamp_hex = hex_data[20:36] if len(hex_data) >= 36 else ""

            # Convert hex timestamp to integer
            if timestamp_hex and all(c in '0123456789ABCDEFabcdef' for c in timestamp_hex):
                return int(timestamp_hex, 16)

            # Fallback to current time if extraction fails
            return int(time.time() * 1000)

        except Exception as e:
            logger.error(f"Error extracting timestamp: {e}")
            return int(time.time() * 1000)

    async def _process_device_queue(self, device_imei: str):
        """Process messages in the device queue"""
        try:
            messages_processed = 0

            while not self.device_queues[device_imei].empty():
                # Get the next message
                timestamp, data, writer = await self.device_queues[device_imei].get()

                # Process the message
                record_number = await self._process_single_message(device_imei, data)

                # Send acknowledgment
                try:
                    record_response = (record_number).to_bytes(4, byteorder="big")
                    writer.write(record_response)
                    await writer.drain()
                except Exception as e:
                    logger.error(f"Error sending acknowledgment to device {device_imei}: {e}")

                messages_processed += 1

                # Limit batch size to prevent blocking
                if messages_processed >= 10:
                    # Schedule continuation
                    asyncio.create_task(self._process_device_queue(device_imei))
                    return

            if messages_processed > 0:
                logger.info(f"Processed {messages_processed} messages for device {device_imei}")

            # Mark processing as complete
            setattr(self, f"processing_{device_imei}", False)

        except Exception as e:
            logger.error(f"Error processing device queue for {device_imei}: {e}")
            # Mark processing as complete even on error
            setattr(self, f"processing_{device_imei}", False)

    async def _process_single_message(self, device_imei: str, data: bytes) -> int:
        """Process a single message and return the record count"""
        try:
            # Convert data to hex
            hex_data = data.hex()

            # Check for duplicates based on coordinates
            location_hash = await self._get_location_hash(device_imei, hex_data)

            # If we've seen this exact location data before, it's a duplicate
            if location_hash in processed_packets:
                logger.info(f"Skipping duplicate packet from {device_imei}")
                return 1  # Return 1 as record count

            # Add to processed packets
            processed_packets.add(location_hash)

            # Limit cache size
            if len(processed_packets) > MAX_CACHE_SIZE:
                # Remove oldest entries (convert to list first)
                to_remove = list(processed_packets)[:MAX_CACHE_SIZE // 2]
                for item in to_remove:
                    processed_packets.remove(item)

            # Instead of using the process pool directly with a method that might contain
            # unpicklable objects (like locks), use a standalone function
            # that only receives simple data types
            loop = asyncio.get_event_loop()

            # Call the standalone parsing function that doesn't contain any unpicklable objects
            result = await loop.run_in_executor(
                None,  # Use default thread pool instead of process pool to avoid pickling issues
                self.codec_8e_parser,  # Use the class method directly in a thread
                hex_data,
                device_imei
            )

            # In a real implementation, we would get back both the record count
            # and the parsed data to store
            if isinstance(result, tuple) and len(result) == 2:
                record_number, io_dict = result

                # Store the data asynchronously
                if io_dict:
                    asyncio.create_task(self._store_data(io_dict, device_imei))

                return record_number
            else:
                # Fallback for simplified implementation
                return result if isinstance(result, int) else 1

        except Exception as e:
            logger.error(f"Error processing message for device {device_imei}: {e}")
            return 1  # Return 1 as a safe default

    async def _store_data(self, io_dict: Dict[str, Any], device_imei: str):
        """Store the parsed data in both live and historical storage"""
        try:
            # Update live data
            await update_live_data(io_dict, device_imei)

            # Store historical data
            await store_historical_data(io_dict, device_imei)

            logger.debug(f"Stored data for device {device_imei}")
        except Exception as e:
            logger.error(f"Error storing data for device {device_imei}: {e}")

    async def _get_location_hash(self, device_imei: str, hex_data: str) -> str:
        """Create a hash based on device IMEI and location data"""
        try:
            # Extract timestamp (typically at position 20-36)
            timestamp_hex = hex_data[20:36] if len(hex_data) >= 36 else ""

            # Extract location data (typically after timestamp and priority)
            # In codec 8E, longitude is at position 38-46 and latitude at 46-54
            longitude_hex = hex_data[38:46] if len(hex_data) >= 46 else ""
            latitude_hex = hex_data[46:54] if len(hex_data) >= 54 else ""

            # Create a hash that includes IMEI, timestamp, and location
            hash_input = f"{device_imei}_{timestamp_hex}_{longitude_hex}_{latitude_hex}"
            return hashlib.md5(hash_input.encode()).hexdigest()

        except Exception as e:
            logger.error(f"Error creating location hash: {e}")
            # Fallback to a simpler hash
            return hashlib.md5(f"{device_imei}_{hex_data[:100]}".encode()).hexdigest()

    def _parse_codec_packet(self, hex_data: str, device_imei: str):
        """
        Parse a codec 8E packet (runs in a separate process)
        Returns a tuple of (record_count, data_dict) or just record_count
        """
        logger.info(f"Parsing packet for device {device_imei}")

        try:
            # This is where we would call the existing codec_8e_parser function
            # For now, we'll implement a simplified version that extracts basic data
            # and returns it for async storage

            # In a real implementation, you would import and call:
            # record_count = codec_8e_parser(hex_data, device_imei, "SERVER")
            # return record_count

            # Simplified implementation for demonstration
            return self.codec_8e_parser(hex_data, device_imei)

        except Exception as e:
            logger.error(f"Error parsing codec packet: {e}")
            return 1  # Return 1 as a safe default

    def codec_8e_parser(self, hex_data: str, device_imei: str):
        """
        Simplified codec parser that extracts basic data
        This is a placeholder for the actual codec_8e_parser function
        Returns a tuple of (record_count, data_dict)
        """
        try:
            # Use hex_data instead of codec_8E_packet (variable name mismatch)
            codec_8E_packet = hex_data  # Rename for compatibility with existing code

            zero_bytes = codec_8E_packet[:8]
            # print()
            # print (str("zero bytes = " + zero_bytes))

            data_field_length = int(codec_8E_packet[8:8+8], 16)
            # print (f"data field lenght = {data_field_length} bytes")
            codec_type = str(codec_8E_packet[16:16+2])
            # print (f"codec type = {codec_type}")

            data_step = 4
            if codec_type == "08":
                data_step = 2
            else:
                pass

            number_of_records = int(codec_8E_packet[18:18+2], 16)
            # print (f"number of records = {number_of_records}")

            record_number = 1
            avl_data_start = codec_8E_packet[20:]
            data_field_position = 0
            while data_field_position < (2*data_field_length-6):
                io_dict = {}
                io_dict["device_IMEI"] = device_imei
                # print()
                # print (f"data from record {record_number}")
                # print (f"########################################")

                timestamp = avl_data_start[data_field_position:data_field_position+16]
                io_dict["last_update"] = time_stamper_for_json()
                io_dict["_timestamp_"] = device_time_stamper(timestamp)
                # print (f"timestamp = {device_time_stamper(timestamp)}")
                io_dict["_rec_delay_"] = record_delay_counter(timestamp)
                data_field_position += len(timestamp)

                priority = avl_data_start[data_field_position:data_field_position+2]
                io_dict["priority"] = int(priority, 16)
                # print (f"record priority = {int(priority, 16)}")
                data_field_position += len(priority)

                longitude = avl_data_start[data_field_position:data_field_position+8]
            #	io_dict["longitude"] = struct.unpack('>i', bytes.fromhex(longitude))[0]
            #	print (f"longitude = {struct.unpack('>i', bytes.fromhex(longitude))[0]}")
                io_dict["longitude"] = coordinate_formater(longitude)
                # print (f"longitude = {coordinate_formater(longitude)}")
                data_field_position += len(longitude)

                latitude = avl_data_start[data_field_position:data_field_position+8]
            #	print (f"latitude = {struct.unpack('>i', bytes.fromhex(latitude))[0]}")
            #	io_dict["latitude"] = struct.unpack('>i', bytes.fromhex(latitude))[0]
                io_dict["latitude"] = coordinate_formater(latitude)
                # print (f"latitude = {coordinate_formater(latitude)}")
                data_field_position += len(latitude)

                altitude = avl_data_start[data_field_position:data_field_position+4]
                # print(f"altitude = {int(altitude, 16)}")
                io_dict["altitude"] = int(altitude, 16)
                data_field_position += len(altitude)

                angle = avl_data_start[data_field_position:data_field_position+4]
                # print(f"angle = {int(angle, 16)}")
                io_dict["angle"] = int(angle, 16)
                data_field_position += len(angle)

                satelites = avl_data_start[data_field_position:data_field_position+2]
                # print(f"satelites = {int(satelites, 16)}")
                io_dict["satelites"] = int(satelites, 16)
                data_field_position += len(satelites)

                speed = avl_data_start[data_field_position:data_field_position+4]
                io_dict["speed"] = int(speed, 16)
                # print(f"speed = {int(speed, 16)}")
                data_field_position += len(speed)

                # Parse the event ID
                event_io_id = avl_data_start[data_field_position:data_field_position+data_step]
                event_id = int(event_io_id, 16)
                io_dict["eventID"] = int(event_io_id, 16)
                # print(f"event ID = {int(event_io_id, 16)}")
                data_field_position += len(event_io_id)

                total_io_elements = avl_data_start[data_field_position:data_field_position+data_step]
                total_io_elements_parsed = int(total_io_elements, 16)
                # print(f"total I/O elements in record {record_number} = {total_io_elements_parsed}")
                data_field_position += len(total_io_elements)

                byte1_io_number = avl_data_start[data_field_position:data_field_position+data_step]
                byte1_io_number_parsed = int(byte1_io_number, 16)
                # print(f"1 byte io count = {byte1_io_number_parsed}")
                data_field_position += len(byte1_io_number)

                if byte1_io_number_parsed > 0:
                    i = 1
                    while i <= byte1_io_number_parsed:
                        key = avl_data_start[data_field_position:data_field_position+data_step]
                        data_field_position += len(key)
                        value = avl_data_start[data_field_position:data_field_position+2]

                        io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
                        data_field_position += len(value)
                        # print (f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
                        i += 1
                else:
                    pass

                byte2_io_number = avl_data_start[data_field_position:data_field_position+data_step]
                byte2_io_number_parsed = int(byte2_io_number, 16)
                # print(f"2 byte io count = {byte2_io_number_parsed}")
                data_field_position += len(byte2_io_number)

                if byte2_io_number_parsed > 0:
                    i = 1
                    while i <= byte2_io_number_parsed:
                        key = avl_data_start[data_field_position:data_field_position+data_step]
                        data_field_position += len(key)

                        value = avl_data_start[data_field_position:data_field_position+4]
                        io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
                        data_field_position += len(value)
                        # print (f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
                        i += 1
                else:
                    pass

                byte4_io_number = avl_data_start[data_field_position:data_field_position+data_step]
                byte4_io_number_parsed = int(byte4_io_number, 16)
                # print(f"4 byte io count = {byte4_io_number_parsed}")
                data_field_position += len(byte4_io_number)

                if byte4_io_number_parsed > 0:
                    i = 1
                    while i <= byte4_io_number_parsed:
                        key = avl_data_start[data_field_position:data_field_position+data_step]
                        data_field_position += len(key)

                        value = avl_data_start[data_field_position:data_field_position+8]
                        io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
                        data_field_position += len(value)
                        # print(f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
                        i += 1
                else:
                    pass

                byte8_io_number = avl_data_start[data_field_position:data_field_position+data_step]
                byte8_io_number_parsed = int(byte8_io_number, 16)
                # print(f"8 byte io count = {byte8_io_number_parsed}")
                data_field_position += len(byte8_io_number)

                if byte8_io_number_parsed > 0:
                    i = 1
                    while i <= byte8_io_number_parsed:
                        key = avl_data_start[data_field_position:data_field_position+data_step]
                        data_field_position += len(key)

                        value = avl_data_start[data_field_position:data_field_position+16]
                        io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
                        data_field_position += len(value)
                        # print(f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
                        i += 1
                else:
                    pass

                if codec_type.upper() == "8E":

                    byteX_io_number = avl_data_start[data_field_position:data_field_position+4]
                    byteX_io_number_parsed = int(byteX_io_number, 16)
                    # print(f"X byte io count = {byteX_io_number_parsed}")
                    data_field_position += len(byteX_io_number)

                    if byteX_io_number_parsed > 0:
                        i = 1
                        while i <= byteX_io_number_parsed:
                            key = avl_data_start[data_field_position:data_field_position+4]
                            data_field_position += len(key)

                            value_length = avl_data_start[data_field_position:data_field_position+4]
                            data_field_position += 4
                            value = avl_data_start[data_field_position:data_field_position+(2*(int(value_length, 16)))]
                            io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
                            data_field_position += len(value)
                            # print(f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
                        #	print (f"data field postition = {data_field_position}")
                        #	print (f"data_field_length = {2*data_field_length}")
                            i += 1
                    else:
                        pass
                else:
                    pass

                record_number += 1

                # # Format coordinates
                # latitude = coordinate_formater(latitude)
                # longitude = coordinate_formater(longitude)
                # current_point = Point(float(longitude), float(latitude))


                # if event_id != 0 and (event_id == 249 or event_id == 247 or event_id == 246 or event_id == 252 or event_id == 318):
                #     send_event_to_api(device_imei, io_dict)

                total_records_parsed = int(avl_data_start[data_field_position:data_field_position+2], 16)
                # print()
                # print(f"total parsed records = {total_records_parsed}")
                # print()
                # return int(number_of_records)
                return total_records_parsed, io_dict
        except Exception as e:
            logger.error(f"Error in  codec parser: {e}")
            return (0, None)

# JSON Data Handling Functions
async def update_live_data(io_dict: Dict[str, Any], device_imei: str):
    """
    Update live data for a device with optimized async I/O handling.
    Uses in-memory caching to reduce disk writes.
    """
    global live_data_cache, last_write_time

    live_data_path = DATA_PATH
    live_data_file = LIVE_DATA_FILE
    file_path = os.path.join(live_data_path, live_data_file)
    temp_file_path = file_path + ".tmp"

    # Ensure the directory exists
    os.makedirs(live_data_path, exist_ok=True)

    async with live_data_lock:
        current_time = time.time()

        # Read from cache or file if cache is empty
        if not live_data_cache:
            # Read existing data with recovery mechanism
            if os.path.exists(file_path):
                try:
                    async with aiofiles.open(file_path, "r") as file:
                        content = await file.read()
                        live_data_cache = json.loads(content)
                except (json.JSONDecodeError, Exception) as e:
                    logger.error(f"Error reading live data file: {e}")
                    # Create backup of corrupted file
                    backup_file_path = file_path + f".bak.{int(time.time())}"
                    try:
                        await aiofiles.os.rename(file_path, backup_file_path)
                    except OSError:
                        pass

                    # Start with empty cache
                    live_data_cache = {}
            else:
                live_data_cache = {}

        # Sanitize input dictionary
        io_dict = sanitize_dict(io_dict)

        # Skip update if no new data is provided
        if not io_dict:
            logger.warning(f"No valid data to update for device {device_imei}. Skipping update.")
            return

        # Ensure the device IMEI exists in live_data
        if device_imei not in live_data_cache:
            live_data_cache[device_imei] = {}

        # Update data from io_dict
        live_data_cache[device_imei].update(io_dict)

        # Only write to disk if enough time has passed since last write
        if current_time - last_write_time >= write_interval:
            # Write data atomically with multiple safeguards
            try:
                # First write to temporary file
                async with aiofiles.open(temp_file_path, "w") as temp_file:
                    # Verify data is JSON serializable before writing
                    json_str = json.dumps(live_data_cache, ensure_ascii=False, separators=(',', ':'))
                    await temp_file.write(json_str)
                    await temp_file.flush()

                # Replace the original file with the temporary file
                await aiofiles.os.replace(temp_file_path, file_path)
                last_write_time = current_time

            except Exception as e:
                logger.error(f"Error writing JSON data: {e}")
                if os.path.exists(temp_file_path):
                    try:
                        await aiofiles.os.remove(temp_file_path)
                    except OSError:
                        pass

async def store_historical_data(io_dict: Dict[str, Any], device_imei: str):
    """
    Store historical data for a device with optimized async I/O handling.
    Uses caching to reduce disk reads/writes and deduplication to prevent duplicates.
    """
    # Static cache for historical data
    if not hasattr(store_historical_data, "daily_data_cache"):
        store_historical_data.daily_data_cache = {}
        store_historical_data.dates_cache = {}
        store_historical_data.last_write_time = {}
        store_historical_data.write_interval = 2.0  # Seconds between writes

    try:
        # Get current date in Italy timezone
        italy_timezone = pytz.timezone('Europe/Rome')
        current_date = datetime.datetime.now(italy_timezone).strftime('%d-%m-%Y')

        # Set up paths
        history_path = f"{HISTORY_PATH}/{device_imei}"
        date_file = "dates.json"
        data_file = f"{current_date}.json"

        date_path = os.path.join(history_path, date_file)
        data_path = os.path.join(history_path, data_file)

        # Ensure directory exists
        os.makedirs(history_path, exist_ok=True)

        # Current time for write interval checks
        current_time = time.time()

        # Cache key for this device and date
        cache_key = f"{device_imei}_{current_date}"

        # Initialize cache for this device if needed
        if device_imei not in store_historical_data.dates_cache:
            store_historical_data.dates_cache[device_imei] = []

            # Load existing dates if available
            if os.path.exists(date_path):
                try:
                    async with aiofiles.open(date_path, "r") as file:
                        content = await file.read()
                        store_historical_data.dates_cache[device_imei] = json.loads(content)
                except (json.JSONDecodeError, Exception) as e:
                    logger.error(f"Error reading dates file: {e}")
                    store_historical_data.dates_cache[device_imei] = []

        # Add current date to dates list if not already there
        if current_date not in store_historical_data.dates_cache[device_imei]:
            store_historical_data.dates_cache[device_imei].append(current_date)

            # Write dates file
            if current_time - store_historical_data.last_write_time.get(f"{device_imei}_dates", 0) >= store_historical_data.write_interval:
                sorted_dates = sorted(
                    store_historical_data.dates_cache[device_imei],
                    key=lambda d: datetime.datetime.strptime(d, '%d-%m-%Y'),
                    reverse=True
                )

                # Write dates atomically
                temp_date_path = date_path + ".tmp"
                try:
                    async with aiofiles.open(temp_date_path, "w") as file:
                        await file.write(json.dumps(sorted_dates, separators=(',', ':')))
                    await aiofiles.os.replace(temp_date_path, date_path)
                    store_historical_data.last_write_time[f"{device_imei}_dates"] = current_time
                except Exception as e:
                    logger.error(f"Error writing dates file: {e}")
                    if os.path.exists(temp_date_path):
                        await aiofiles.os.remove(temp_date_path)

        # Initialize cache for this device and date if needed
        if cache_key not in store_historical_data.daily_data_cache:
            store_historical_data.daily_data_cache[cache_key] = []

            # Load existing data if available
            if os.path.exists(data_path):
                try:
                    async with aiofiles.open(data_path, "r") as file:
                        content = await file.read()
                        store_historical_data.daily_data_cache[cache_key] = json.loads(content)
                except (json.JSONDecodeError, Exception) as e:
                    logger.error(f"Error reading daily data file: {e}")
                    store_historical_data.daily_data_cache[cache_key] = []

        # Sanitize the input dictionary
        sanitized_io_dict = sanitize_dict(io_dict)

        # Skip if no valid data
        if not sanitized_io_dict:
            return

        # Check for duplicates (simple check based on all fields)
        is_duplicate = False
        if store_historical_data.daily_data_cache[cache_key]:
            last_entry = store_historical_data.daily_data_cache[cache_key][-1]

            # Check if coordinates are the same (main duplicate indicator)
            if (last_entry.get('longitude') == sanitized_io_dict.get('longitude') and
                last_entry.get('latitude') == sanitized_io_dict.get('latitude')):
                is_duplicate = True

        # Only append if not a duplicate
        if not is_duplicate:
            # Add to the cache
            store_historical_data.daily_data_cache[cache_key].append(sanitized_io_dict)

            # Only write to disk if enough time has passed
            if current_time - store_historical_data.last_write_time.get(cache_key, 0) >= store_historical_data.write_interval:
                # Write daily data atomically
                temp_data_path = data_path + ".tmp"
                try:
                    async with aiofiles.open(temp_data_path, "w") as file:
                        await file.write(json.dumps(store_historical_data.daily_data_cache[cache_key], separators=(',', ':')))
                    await aiofiles.os.replace(temp_data_path, data_path)
                    store_historical_data.last_write_time[cache_key] = current_time
                except Exception as e:
                    logger.error(f"Error writing daily data file: {e}")
                    if os.path.exists(temp_data_path):
                        await aiofiles.os.remove(temp_data_path)

    except Exception as e:
        logger.error(f"Error in store_historical_data: {e}")

def sanitize_dict(input_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Deep sanitize the input dictionary to handle malformed JSON data.
    """
    if not isinstance(input_dict, dict):
        return {}

    result = {}
    for key, value in input_dict.items():
        # Skip None values
        if value is None:
            continue

        # Handle nested dictionaries
        if isinstance(value, dict):
            sanitized_value = sanitize_dict(value)
            if sanitized_value:  # Only add non-empty dictionaries
                result[key] = sanitized_value
        # Handle lists
        elif isinstance(value, list):
            sanitized_list = []
            for item in value:
                if isinstance(item, dict):
                    sanitized_item = sanitize_dict(item)
                    if sanitized_item:  # Only add non-empty dictionaries
                        sanitized_list.append(sanitized_item)
                elif item is not None:  # Skip None values in lists
                    sanitized_list.append(item)
            if sanitized_list:  # Only add non-empty lists
                result[key] = sanitized_list
        # Handle other types
        else:
            # Try to ensure the value is JSON serializable
            try:
                json.dumps({key: value})
                result[key] = value
            except (TypeError, OverflowError):
                # Convert to string if not serializable
                try:
                    result[key] = str(value)
                except:
                    # Skip if we can't even convert to string
                    pass

    return result

# Utility functions for time handling
def time_stamper_for_json():
    """Get current time in Italy timezone formatted for JSON"""
    # Define the timezone for Italy
    italy_timezone = pytz.timezone('Europe/Rome')

    # Get the current time in Italy's timezone
    current_italy_time = datetime.datetime.now(italy_timezone)

    # Format the time in the desired Italian format
    italy_time_stamp = current_italy_time.strftime('%d/%m/%Y %H:%M')

    return italy_time_stamp

def device_time_stamper(timestamp_hex):
    """Convert device timestamp to human-readable format"""
    try:
        # Convert the hex timestamp to milliseconds and then to seconds
        timestamp_ms = int(timestamp_hex, 16) / 1000  # Fixed variable name

        # Use timezone-aware datetime for UTC
        timestamp_utc = datetime.datetime.fromtimestamp(timestamp_ms, datetime.timezone.utc)

        # Define the timezone for Italy
        italy_timezone = pytz.timezone('Europe/Rome')

        # Convert UTC to Italy timezone
        timestamp_italy = timestamp_utc.astimezone(italy_timezone)

        # Format both timestamps
        formatted_timestamp_italy = timestamp_italy.strftime("%d/%m/%Y %H:%M")  # Italian format
        formatted_timestamp_utc = timestamp_utc.strftime("%d/%m/%Y %H:%M")  # UTC format

        # Combine both formatted timestamps
        formatted_timestamp = f"{formatted_timestamp_italy} (Italy) / {formatted_timestamp_utc} (UTC)"

        return formatted_timestamp
    except Exception as e:
        logger.error(f"Error in device_time_stamper: {e}")
        return time_stamper_for_json()  # Fallback to current time

def record_delay_counter(timestamp_hex):
    """Calculate delay between record timestamp and current time"""
    try:
        timestamp_ms = int(timestamp_hex, 16) / 1000
        current_server_time = datetime.datetime.now().timestamp()
        return f"{int(current_server_time - timestamp_ms)} seconds"
    except Exception as e:
        logger.error(f"Error calculating record delay: {e}")
        return "unknown"

def coordinate_formater(coordinate_hex):
    """Format coordinates from hex to decimal"""
    try:
        # Convert hex to signed integer
        value = int(coordinate_hex, 16)
        if value > 0x7FFFFFFF:
            value -= 0x100000000

        # Convert to decimal degrees
        return value / 10000000.0
    except Exception as e:
        logger.error(f"Error formatting coordinate: {e}")
        return 0.0

def sorting_hat(key, value):
    """Sort and convert IO values based on their key"""
    try:
        # Handle different types of values based on key
        if key in [16, 21, 66, 67, 68, 69, 113, 181, 182, 200, 239, 240, 241]:
            # These are common keys that might need special handling
            return int(value, 16)
        else:
            # Default handling for other keys
            return int(value, 16)
    except Exception as e:
        logger.error(f"Error in sorting_hat for key {key}: {e}")
        return 0

# Main entry point
async def main():
    """Main entry point for the async Teltonika parser"""
    global live_data_lock

    # Initialize locks
    live_data_lock = asyncio.Lock()

    # Create parser instance
    parser = TeltonikaParser()

    # Start the server
    host = '0.0.0.0'
    port = 2021

    logger.info(f"Starting Teltonika parser server on {host}:{port}")
    await parser.start_server(host, port)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        raise

"""
INTEGRATION NOTES:

To fully integrate this async parser with the existing codec parser:

1. Import the existing codec_8e_parser function:
   ```python
   from existing_module import codec_8e_parser
   ```

2. Replace the _parse_codec_packet method with:
   ```python
   def _parse_codec_packet(self, hex_data: str, device_imei: str):
       try:
           # Call the existing parser to get record count
           record_count = codec_8e_parser(hex_data, device_imei, "SERVER")

           # For integration with async storage, you would need to modify
           # the codec_8e_parser to return both the record count and the parsed data
           # or extract the data from a global variable if that's how it's implemented

           # Return just the record count for now
           return record_count
       except Exception as e:
           logger.error(f"Error parsing codec packet: {e}")
           return 1
   ```

3. If you want to fully integrate with async storage, modify the existing codec_8e_parser
   to return both the record count and the parsed data dictionary:
   ```python
   def codec_8e_parser(codec_8E_packet, device_imei, props):
       # Existing parsing logic...

       # Instead of calling json_printer directly:
       # json_printer(io_dict, device_imei)

       # Return both the record count and the data
       return record_number, io_dict
   ```

4. Then update the _parse_codec_packet method to use this:
   ```python
   def _parse_codec_packet(self, hex_data: str, device_imei: str):
       try:
           # Call the modified parser to get both record count and data
           record_count, io_dict = codec_8e_parser(hex_data, device_imei, "SERVER")
           return record_count, io_dict
       except Exception as e:
           logger.error(f"Error parsing codec packet: {e}")
           return 1, None
   ```

This approach maintains compatibility with the existing parser while adding
the benefits of async I/O and separation of concerns.
"""
