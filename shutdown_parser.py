#!/usr/bin/env python3
"""
Graceful Shutdown Script for GPS Parser

This script allows you to gracefully shutdown the GPS parsing script
from outside the tmux session without losing any GPS data.

Usage:
    python3 shutdown_parser.py

The script will:
1. Create a shutdown signal file
2. Wait for the parser to detect it and shutdown gracefully
3. Confirm the shutdown was successful
"""

import os
import time
import sys

# Base path - should match the one in index.py
BASE_PATH = "/home/<USER>/ControlOne/public_html/public/"

def trigger_graceful_shutdown():
    """
    Trigger graceful shutdown by creating a signal file.
    """
    shutdown_file = BASE_PATH + "shutdown_signal.txt"
    
    print("🛑 Triggering graceful shutdown of GPS parser...")
    print("📊 This will ensure all GPS data is processed and saved before shutdown.")
    
    try:
        # Create the shutdown signal file
        with open(shutdown_file, 'w') as f:
            f.write(f"Shutdown requested at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"✅ Shutdown signal file created: {shutdown_file}")
        print("⏳ Waiting for parser to detect shutdown signal...")
        
        # Wait for the file to be removed (indicating parser detected it)
        max_wait_time = 30  # Maximum 30 seconds
        wait_time = 0
        
        while os.path.exists(shutdown_file) and wait_time < max_wait_time:
            time.sleep(1)
            wait_time += 1
            if wait_time % 5 == 0:
                print(f"⏳ Still waiting... ({wait_time}s)")
        
        if not os.path.exists(shutdown_file):
            print("✅ Shutdown signal detected by parser!")
            print("📊 Parser is now processing remaining GPS data and shutting down gracefully.")
            print("🔄 This may take a few moments depending on queue size.")
        else:
            print("⚠️  Warning: Shutdown signal file still exists after 30 seconds.")
            print("   The parser may not be running or may be busy processing data.")
            print("   You can check the tmux session to see the shutdown progress.")
            
            # Clean up the file
            try:
                os.remove(shutdown_file)
                print("🧹 Cleaned up shutdown signal file.")
            except:
                pass
        
    except Exception as e:
        print(f"❌ Error creating shutdown signal: {e}")
        return False
    
    return True

def check_parser_status():
    """
    Check if the parser is likely running by looking for process.
    """
    try:
        import subprocess
        result = subprocess.run(['pgrep', '-f', 'index.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"📍 Found {len(pids)} parser process(es): {', '.join(pids)}")
            return True
        else:
            print("ℹ️  No parser processes found.")
            return False
    except Exception as e:
        print(f"⚠️  Could not check parser status: {e}")
        return None

def main():
    print("=" * 60)
    print("🚀 GPS Parser Graceful Shutdown Tool")
    print("=" * 60)
    
    # Check if parser is running
    parser_running = check_parser_status()
    
    if parser_running is False:
        print("❌ Parser doesn't appear to be running.")
        print("   If it's running in tmux, you can shutdown from within the session using Ctrl+C")
        return
    
    # Confirm shutdown
    print("\n⚠️  This will gracefully shutdown the GPS parser.")
    print("   All queued GPS data will be processed and saved before shutdown.")
    
    response = input("\nDo you want to proceed? (y/N): ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("❌ Shutdown cancelled.")
        return
    
    # Trigger shutdown
    success = trigger_graceful_shutdown()
    
    if success:
        print("\n✅ Graceful shutdown initiated successfully!")
        print("📋 Check the tmux session for detailed shutdown progress.")
        print("🔄 The parser will finish processing all GPS data before stopping.")
    else:
        print("\n❌ Failed to initiate graceful shutdown.")
        print("   You may need to shutdown manually from the tmux session using Ctrl+C")

if __name__ == "__main__":
    main()
