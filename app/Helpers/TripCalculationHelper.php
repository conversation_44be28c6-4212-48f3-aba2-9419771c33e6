<?php

namespace App\Helpers;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class TripCalculationHelper
{
    /**
     * Calculate trips and stops from history data using consistent logic
     *
     * @param array $historyData The GPS history data for the day
     * @param string $date The date in d-m-Y format
     * @return array Contains 'stops' and 'trips' arrays
     */
    public static function calculateTripsAndStops($historyData, $date)
    {
        $stops = [];
        $trips = [];
        $firstValidData = null;
        $lastValidData = null;
        $tripStartOdometer = null;
        $tripStartFuel = null;

        // Variables for tracking stops and trips
        $lastStatus = null;         // For eventID 250 tracking
        $tripStartTime = null;      // When a stop begins
        $lastTripStop = null;       // For backup method tracking
        $currentStopStart = null;   // For speed/ignition based detection
        $currentTripStart = null;   // When a trip begins
        $tripStartLocation = null;  // Starting location of a trip

        // Check if the vehicle was already stopped at the beginning of the day
        $initialStopDetected = false;

        foreach ($historyData as $data) {
            // Skip invalid data
            if (!isset($data['latitude']) || !isset($data['longitude'])) {
                continue;
            }

            // Keep track of first and last valid data for overall trip metrics
            if (!$firstValidData) {
                $firstValidData = $data;
                $tripStartOdometer = getOdometerValue($firstValidData);
                $tripStartFuel = getFuelConsumption($firstValidData);

                // Check if the vehicle is already stopped at the beginning of the day
                $speed = isset($data['speed']) ? (float)$data['speed'] : 0;
                $ignition = isset($data['239']) ? (int)$data['239'] : 0;
                $timestamp = parseFlexibleTimestamp($data['last_update']);

                // If the first record shows the vehicle is stopped (speed < 1 and ignition on)
                if ($speed < 1 && $ignition === 1) {
                    $initialStopDetected = true;

                    // Create a stop starting from the beginning of the day
                    $dayStart = $timestamp->copy()->startOfDay();
                    $stopDuration = $dayStart->diffForHumans($timestamp, true);
                    $stopDurationMinutes = $dayStart->diffInMinutes($timestamp);

                    if ($stopDurationMinutes > 1) {
                        $stops[] = [
                            'time' => $dayStart->format('H:i'),
                            'start_time' => $dayStart,
                            'end_time' => $timestamp,
                            'duration' => $stopDuration,
                            'location' => [
                                'lat' => $data['latitude'],
                                'lng' => $data['longitude'],
                                'address' => getAddressFromCoordinates($data['latitude'], $data['longitude'])
                            ],
                            'odometer' => getOdometerValue($data),
                            'fuel' => getFuelConsumption($data),
                            'trip_distance' => 0, // No distance since beginning of day
                            'trip_fuel' => 0 // No fuel consumption since beginning of day
                        ];
                    }

                    // Set up for next stop/trip detection
                    $lastTripStop = [
                        'timestamp' => $timestamp,
                        'latitude' => $data['latitude'],
                        'longitude' => $data['longitude'],
                        'odometer' => getOdometerValue($data),
                        'fuel' => getFuelConsumption($data),
                        'totalOdometer' => getOdometerValue($data)
                    ];
                    $currentStopStart = $lastTripStop;
                }

                // If eventID 250 is present in the first record, set the initial status
                if (isset($data['eventID']) && $data['eventID'] == 250 && isset($data['250'])) {
                    $lastStatus = (int)$data['250'];

                    // If status is 0 (stopped), create an initial stop
                    if ($lastStatus === 0 && !$initialStopDetected) {
                        $initialStopDetected = true;
                        $timestamp = parseFlexibleTimestamp($data['last_update']);
                        $dayStart = $timestamp->copy()->startOfDay();

                        $tripStartTime = $dayStart;
                        $lastTripStop = [
                            'timestamp' => $dayStart,
                            'latitude' => $data['latitude'],
                            'longitude' => $data['longitude'],
                            'odometer' => getOdometerValue($data),
                            'fuel' => getFuelConsumption($data),
                            'totalOdometer' => getOdometerValue($data)
                        ];
                    }
                }
            }
            $lastValidData = $data;

            // Parse common values
            $timestamp = parseFlexibleTimestamp($data['last_update']);
            $speed = isset($data['speed']) ? (float)$data['speed'] : 0;
            $ignition = isset($data['239']) ? (int)$data['239'] : 0;
            $currentOdometer = getOdometerValue($data);
            $currentFuel = getFuelConsumption($data);

            // Method 1: Track stops and trips using eventID 250
            if (isset($data['eventID']) && $data['eventID'] == 250 && isset($data['250'])) {
                $currentStatus = (int)$data['250'];

                // Trip Stop (1 -> 0)
                if ($lastStatus === 1 && $currentStatus === 0) {
                    $tripStartTime = $timestamp;
                    // Store stop start data
                    $lastTripStop = [
                        'timestamp' => $timestamp,
                        'latitude' => $data['latitude'],
                        'longitude' => $data['longitude'],
                        'odometer' => $currentOdometer,
                        'fuel' => $currentFuel,
                        'totalOdometer' => $currentOdometer
                    ];

                    // End of trip (1 -> 0)
                    if ($currentTripStart) {
                        $tripData = self::createTripData(
                            $currentTripStart,
                            $timestamp,
                            $tripStartLocation,
                            $data
                        );

                        if ($tripData) {
                            $trips[] = $tripData;
                        }

                        $currentTripStart = null;
                        $tripStartLocation = null;
                    }
                }
                // Trip Start (0 -> 1) and we have a previous stop
                elseif ($lastStatus === 0 && $currentStatus === 1) {
                    // Start of trip (0 -> 1)
                    $currentTripStart = $timestamp;
                    $tripStartLocation = [
                        'latitude' => $data['latitude'],
                        'longitude' => $data['longitude'],
                        'odometer' => getOdometerValue($data),
                        'fuel' => getFuelConsumption($data)
                    ];

                    // Process previous stop if exists
                    if ($tripStartTime) {
                        $stopData = self::createStopData(
                            $tripStartTime,
                            $timestamp,
                            $lastTripStop,
                            $currentOdometer,
                            $currentFuel,
                            $tripStartFuel
                        );

                        if ($stopData) {
                            $stops[] = $stopData;
                        }

                        $tripStartTime = null;
                        $lastTripStop = null;
                    }
                }

                $lastStatus = $currentStatus;
            }

            // Method 2: Backup method - detect stops and trips based on speed and ignition
            // Only use this method if eventID 250 is not available for this record
            if (!isset($data['eventID']) || $data['eventID'] != 250) {
                if ($speed < 1 && $ignition === 1) {
                    // Vehicle is stopped with ignition on
                    if (!$currentStopStart) {
                        $currentStopStart = [
                            'timestamp' => $timestamp,
                            'latitude' => $data['latitude'],
                            'longitude' => $data['longitude'],
                            'odometer' => $currentOdometer,
                            'fuel' => $currentFuel,
                            'totalOdometer' => $currentOdometer
                        ];

                        // If we're not already tracking a stop with method 1
                        if (!$lastTripStop) {
                            $lastTripStop = $currentStopStart;
                        }

                        // If we have an active trip, end it
                        if ($currentTripStart && $tripStartLocation) {
                            $tripData = self::createTripData(
                                $currentTripStart,
                                $timestamp,
                                $tripStartLocation,
                                $data
                            );

                            if ($tripData) {
                                $trips[] = $tripData;
                            }

                            $currentTripStart = null;
                            $tripStartLocation = null;
                        }
                    }
                } elseif ($speed > 5 && $lastTripStop && !$tripStartTime) {
                    // Vehicle started moving again (speed > 5) - this is a trip start
                    $stopData = self::createStopData(
                        Carbon::parse($lastTripStop['timestamp']),
                        $timestamp,
                        $lastTripStop,
                        $currentOdometer,
                        $currentFuel,
                        $tripStartFuel
                    );

                    if ($stopData) {
                        $stops[] = $stopData;
                    }

                    // Start a new trip if we don't have an active one from method 1
                    if (!$currentTripStart) {
                        $currentTripStart = $timestamp;
                        $tripStartLocation = [
                            'latitude' => $data['latitude'],
                            'longitude' => $data['longitude'],
                            'odometer' => getOdometerValue($data),
                            'fuel' => getFuelConsumption($data)
                        ];
                    }

                    $lastTripStop = null;
                    $currentStopStart = null;
                } else if ($speed >= 1 || $ignition !== 1) {
                    // Vehicle is moving or ignition is off
                    $currentStopStart = null;

                    // Start a trip if we don't have one and the vehicle is moving
                    if (!$currentTripStart && $speed > 5) {
                        $currentTripStart = $timestamp;
                        $tripStartLocation = [
                            'latitude' => $data['latitude'],
                            'longitude' => $data['longitude'],
                            'odometer' => getOdometerValue($data),
                            'fuel' => getFuelConsumption($data)
                        ];
                    }
                }
            }
        }

        // Handle ongoing stop and trip at end of day
        $reportDate = Carbon::createFromFormat('d-m-Y', $date);
        $endOfDay = $reportDate->copy()->endOfDay();
        $currentTime = $reportDate->isToday() ? now() : $endOfDay;

        // Handle ongoing stop
        if (($tripStartTime && $lastStatus === 0) || $lastTripStop) {
            $stopData = self::createOngoingStopData(
                $lastTripStop,
                $tripStartTime,
                $lastValidData,
                $currentTime,
                $tripStartFuel
            );

            if ($stopData) {
                $stops[] = $stopData;
            }
        }

        // Handle ongoing trip
        if ($currentTripStart && ($lastStatus === 1 || $tripStartLocation)) {
            $tripData = self::createOngoingTripData(
                $currentTripStart,
                $currentTime,
                $tripStartLocation,
                $lastValidData
            );

            if ($tripData) {
                $trips[] = $tripData;
            }
        }

        return [
            'stops' => $stops,
            'trips' => $trips,
            'firstValidData' => $firstValidData,
            'lastValidData' => $lastValidData,
            'tripStartOdometer' => $tripStartOdometer,
            'tripStartFuel' => $tripStartFuel
        ];
    }

    /**
     * Create trip data structure
     */
    private static function createTripData($startTime, $endTime, $startLocation, $endData)
    {
        $tripDuration = $startTime->diffForHumans($endTime, true);
        $tripDurationMinutes = $startTime->diffInMinutes($endTime);

        // Only add trips with duration greater than 1 minute
        if ($tripDurationMinutes <= 1 || !$startLocation) {
            return null;
        }

        // Calculate trip distance using GPS coordinates
        $tripDistance = self::calculateGpsDistance(
            $startLocation['latitude'],
            $startLocation['longitude'],
            $endData['latitude'],
            $endData['longitude']
        );

        // If GPS distance is too small, calculate based on odometer
        if ($tripDistance < 0.1 && isset($startLocation['odometer'])) {
            $startOdo = $startLocation['odometer'];
            $endOdo = getOdometerValue($endData);
            $tripDistance = self::calculateTripDistance($startOdo, $endOdo);
        }

        // Calculate fuel consumption
        $fuelConsumption = 0;
        if (isset($startLocation['fuel'])) {
            $startFuel = $startLocation['fuel'];
            $endFuel = getFuelConsumption($endData);

            // Calculate fuel consumption - if end fuel is less than start fuel,
            // it means fuel was consumed
            if ($endFuel < $startFuel) {
                $fuelConsumption = $startFuel - $endFuel;
            } else {
                // If end fuel is greater, it might be a refill or sensor error
                // In this case, we'll use a very small consumption value based on distance
                $fuelConsumption = $tripDistance * 0.05; // Estimate 0.05L per km
            }
        }

        return [
            'start_time' => $startTime,
            'end_time' => $endTime,
            'duration' => $tripDuration,
            'duration_minutes' => $tripDurationMinutes,
            'start_location' => [
                'lat' => $startLocation['latitude'],
                'lng' => $startLocation['longitude'],
                'address' => getAddressFromCoordinates($startLocation['latitude'], $startLocation['longitude'])
            ],
            'end_location' => [
                'lat' => $endData['latitude'],
                'lng' => $endData['longitude'],
                'address' => getAddressFromCoordinates($endData['latitude'], $endData['longitude'])
            ],
            'distance' => $tripDistance,
            'fuel_consumption' => $fuelConsumption
        ];
    }

    /**
     * Create stop data structure
     */
    private static function createStopData($startTime, $endTime, $stopLocation, $currentOdometer, $currentFuel, $tripStartFuel)
    {
        $duration = $startTime->diffForHumans($endTime, true);
        $durationMinutes = $startTime->diffInMinutes($endTime);

        // Only add stops with duration greater than 1 minute
        if ($durationMinutes <= 1) {
            return null;
        }

        return [
            'time' => $startTime->format('H:i'),
            'start_time' => $startTime,
            'end_time' => $endTime,
            'duration' => $duration,
            'location' => [
                'lat' => $stopLocation['latitude'],
                'lng' => $stopLocation['longitude'],
                'address' => getAddressFromCoordinates($stopLocation['latitude'], $stopLocation['longitude'])
            ],
            'odometer' => $currentOdometer,
            'fuel' => $currentFuel,
            'trip_distance' => self::calculateTripDistance($stopLocation['odometer'], $currentOdometer), // Distance since stop in km
            'trip_fuel' => max(0, abs($tripStartFuel - $currentFuel)) // Absolute difference in fuel
        ];
    }

    /**
     * Create ongoing stop data structure
     */
    private static function createOngoingStopData($lastTripStop, $tripStartTime, $lastValidData, $currentTime, $tripStartFuel)
    {
        $stopData = $lastTripStop ?? [
            'timestamp' => $tripStartTime,
            'latitude' => $lastValidData['latitude'],
            'longitude' => $lastValidData['longitude'],
            'odometer' => getOdometerValue($lastValidData),
            'fuel' => getFuelConsumption($lastValidData),
        ];

        $stopTimestamp = $stopData['timestamp'] instanceof Carbon ? $stopData['timestamp'] : Carbon::parse($stopData['timestamp']);
        $duration = $stopTimestamp->diffForHumans($currentTime, true);
        $durationMinutes = $stopTimestamp->diffInMinutes($currentTime);

        if ($durationMinutes <= 1) {
            return null;
        }

        $currentOdometer = getOdometerValue($lastValidData);
        $currentFuel = getFuelConsumption($lastValidData);

        return [
            'time' => $stopTimestamp->format('H:i'),
            'start_time' => $stopTimestamp,
            'end_time' => 'Ongoing',
            'duration' => $duration,
            'location' => [
                'lat' => $stopData['latitude'],
                'lng' => $stopData['longitude'],
                'address' => getAddressFromCoordinates($stopData['latitude'], $stopData['longitude'])
            ],
            'odometer' => $currentOdometer,
            'fuel' => $currentFuel,
            'trip_distance' => self::calculateTripDistance($stopData['odometer'], $currentOdometer), // Distance since stop in km
            'trip_fuel' => max(0, abs($tripStartFuel - $currentFuel)) // Absolute difference in fuel
        ];
    }

    /**
     * Create ongoing trip data structure
     */
    private static function createOngoingTripData($currentTripStart, $currentTime, $tripStartLocation, $lastValidData)
    {
        $tripDuration = $currentTripStart->diffForHumans($currentTime, true);
        $tripDurationMinutes = $currentTripStart->diffInMinutes($currentTime);

        // Only add trips with duration greater than 1 minute
        if ($tripDurationMinutes <= 1 || !$tripStartLocation) {
            return null;
        }

        // Calculate trip distance using GPS coordinates
        $tripDistance = self::calculateGpsDistance(
            $tripStartLocation['latitude'],
            $tripStartLocation['longitude'],
            $lastValidData['latitude'],
            $lastValidData['longitude']
        );

        // If GPS distance is too small, calculate based on odometer
        if ($tripDistance < 0.1 && isset($tripStartLocation['odometer'])) {
            $startOdo = $tripStartLocation['odometer'];
            $endOdo = getOdometerValue($lastValidData);
            $tripDistance = self::calculateTripDistance($startOdo, $endOdo);
        }

        // Calculate fuel consumption
        $fuelConsumption = 0;
        if (isset($tripStartLocation['fuel'])) {
            $startFuel = $tripStartLocation['fuel'];
            $endFuel = getFuelConsumption($lastValidData);

            // Calculate fuel consumption - if end fuel is less than start fuel,
            // it means fuel was consumed
            if ($endFuel < $startFuel) {
                $fuelConsumption = $startFuel - $endFuel;
            } else {
                // If end fuel is greater, it might be a refill or sensor error
                // In this case, we'll use a very small consumption value based on distance
                $fuelConsumption = $tripDistance * 0.05; // Estimate 0.05L per km
            }
        }

        return [
            'start_time' => $currentTripStart,
            'end_time' => $currentTime,
            'duration' => $tripDuration,
            'duration_minutes' => $tripDurationMinutes,
            'start_location' => [
                'lat' => $tripStartLocation['latitude'],
                'lng' => $tripStartLocation['longitude'],
                'address' => getAddressFromCoordinates($tripStartLocation['latitude'], $tripStartLocation['longitude'])
            ],
            'end_location' => [
                'lat' => $lastValidData['latitude'],
                'lng' => $lastValidData['longitude'],
                'address' => getAddressFromCoordinates($lastValidData['latitude'], $lastValidData['longitude'])
            ],
            'distance' => $tripDistance,
            'fuel_consumption' => $fuelConsumption
        ];
    }

    /**
     * Calculate distance between two GPS coordinates using Haversine formula
     */
    private static function calculateGpsDistance($lat1, $lon1, $lat2, $lon2)
    {
        if (!$lat1 || !$lon1 || !$lat2 || !$lon2) {
            return 0;
        }

        // Convert latitude and longitude from degrees to radians
        $lat1 = deg2rad((float)$lat1);
        $lon1 = deg2rad((float)$lon1);
        $lat2 = deg2rad((float)$lat2);
        $lon2 = deg2rad((float)$lon2);

        // Haversine formula
        $dlat = $lat2 - $lat1;
        $dlon = $lon2 - $lon1;
        $a = sin($dlat / 2) * sin($dlat / 2) + cos($lat1) * cos($lat2) * sin($dlon / 2) * sin($dlon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = 6371 * $c; // Earth radius in km

        return $distance;
    }

    /**
     * Calculate trip distance in km
     */
    private static function calculateTripDistance($startOdometer, $endOdometer)
    {
        if (!$startOdometer || !$endOdometer) {
            return 0;
        }

        // Calculate the difference and convert to km
        $distanceMeters = max(0, $endOdometer - $startOdometer);

        // If the distance is unreasonably large (over 1000 km), it might be an odometer reset
        if ($distanceMeters > 1000000) { // 1000 km in meters
            return 0;
        }

        return $distanceMeters / 1000; // Convert to km
    }
}
