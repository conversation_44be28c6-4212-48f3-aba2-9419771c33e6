<?php

namespace App\Livewire\Panel;

use App\Exports\ReportExport;
use App\Models\Alarm;
use App\Models\VehicleUser;
use App\Services\FuelReportService;
use App\Services\RouteReportService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Pagination\LengthAwarePaginator;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class Reporting extends Component
{
    use WithPagination;
    public $vehicle;
    public $date;
    public $geofences;
    public $alarmType;
    public $reporting_type = "routes";
    public $generate_report = false;



    // Get Mileage based on vehicle type
    private function getMileageForVehicle($vehicleType)
    {
        return match ($vehicleType) {
            'truck', 'bus', 'tractor' => 3,
            'car', 'bike', 'scooter', 'van', 'ambulance' => 11,
            default => 0,
        };
    }


    #[On('selectedVehicleUpdated')]
    public function updateSelectedVehicle($vehicle = null)
    {
        $this->vehicle = $vehicle;
    }

    #[On('selectedGeofencesUpdated')]
    public function updateSelectedGeofences($geofences = null)
    {
        $this->geofences = $geofences;
    }

    public function generateReport()
    {
        $this->generate_report = true;
    }


    public function render()
    {
        $user = auth()->user();
        $reports = collect();
        $alarms = collect();
        $fuelReports = collect();

        $reports = new LengthAwarePaginator($reports->forPage(1, 10), $reports->count(), 10);
        $alarms = new LengthAwarePaginator($alarms->forPage(1, 10), $alarms->count(), 10);
        $fuelReports = new LengthAwarePaginator($fuelReports->forPage(1, 10), $fuelReports->count(), 10);


        if ($this->generate_report == true) {
            if ($this->reporting_type == 'routes') {
                $reports = RouteReportService::generateRouteReports($this->vehicle, $this->date);
            } elseif ($this->reporting_type == 'alarms') {
                $alarms = $this->generateAlarmsReports($user);
            } elseif ($this->reporting_type == 'fuel_consumption') {
                $fuelReports = FuelReportService::generateFuelReports($this->vehicle, $this->date);
            }
            $this->generate_report = false;
        }


        return view('livewire.panel.reporting', compact('reports', 'alarms', 'fuelReports'));
    }

    public function exportToExcel()
    {
        $this->generate_report = true;

        $data = $this->prepareExportData();
        return Excel::download(new ReportExport($data, $this->reporting_type, $this->date), 'report.xlsx');
    }

    public function exportToPDF()
    {
        $this->generate_report = true;

        $data = $this->prepareExportData();
        $pdf = Pdf::loadView('exports.report', ['data' => $data, 'reporting_type' => $this->reporting_type, $this->date]);
        return response()->streamDownload(fn() => print($pdf->stream()), 'report.pdf');
    }


    // Prepare Data for Export (used by both Excel and PDF)
    private function prepareExportData()
    {
        if ($this->reporting_type == 'routes') {
            return RouteReportService::generateRouteReports($this->vehicle, $this->date);
        }

        if ($this->reporting_type == 'fuel_consumption') {
            return FuelReportService::generateFuelReports($this->vehicle, $this->date);
        }
        // Prepare data for alarms
        return Alarm::with('vehicle')
            ->when($this->vehicle, fn($query) => $query->where('vehicle_id', $this->vehicle))
            ->when($this->geofences, fn($query) => $query->whereIn('geofence_id', $this->geofences))
            ->when($this->alarmType, fn($query) => $query->where('alarm_type', $this->alarmType))
            ->when($this->start_date, fn($query) => $query->whereDate('created_at', '>=', $this->start_date))
            ->when($this->end_date, fn($query) => $query->whereDate('created_at', '<=', $this->end_date))
            ->get()
            ->map(fn($alarm) => [
                'Vehicle' => $alarm->vehicle?->license_plate ?? 'N/A',
                'Alarm Type' => __('translations.' . $alarm->alarm_type, ['vehicle' => $alarm->vehicle?->license_plate, 'geofence' => $alarm->geofence?->name]) ?? 'N/A',
                'Triggered At' => $alarm->created_at->format('Y-m-d H:i:s'),
                'Geofence' => $alarm->geofence?->name ?? 'N/A',
                'Location' => $alarm->location ?? 'N/A',
            ]);
    }


    private function generateAlarmsReports($user)
    {
        return Alarm::query()
            ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                $query->whereIn('vehicle_id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
            })
            ->when($this->vehicle, function ($query) {
                $query->where('vehicle_id', $this->vehicle);
            })
            ->when($this->geofences, function ($query) {
                $query->whereIn('geofence_id', $this->geofences);
            })
            ->when($this->alarmType, function ($query) {
                $query->where('alarm_type', $this->alarmType);
            })
            ->when($this->date, function ($query) {
                $query->whereDate('created_at', $this->date);
            })
            // ->when($this->date, function ($query) {
            //     $query->whereDate('created_at', '<=', $this->date);
            // })
            ->latest()
            ->paginate(10);
    }
}
