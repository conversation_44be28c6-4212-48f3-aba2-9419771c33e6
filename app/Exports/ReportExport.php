<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class ReportExport implements WithMultipleSheets
{
    protected $data;
    protected $reportingType;
    protected $date;

    public function __construct($data, $reportingType, $date)
    {
        $this->data = $data;
        $this->reportingType = $reportingType;
        $this->date = $date;
    }

    public function sheets(): array
    {
        $sheets = [];

        if ($this->reportingType == 'routes') {
            $sheets[] = new ReportSummarySheet($this->data['summary'], $this->date);
            // Add Geofence Events sheet if events exist
            if (isset($this->data['summary']['geofence_events']) && count($this->data['summary']['geofence_events']) > 0) {
                $sheets[] = new GeofenceEventsSheet($this->data['summary']['geofence_events']);
            }
            $sheets[] = new ReportDetailsSheet($this->data['details'], $this->reportingType);
        } elseif ($this->reportingType == 'alarms') {
            $sheets[] = new ReportDetailsSheet($this->data, $this->reportingType);
        } elseif ($this->reportingType == 'fuel_consumption') {
            $sheets[] = new FuelConsumptionSummarySheet($this->data['summary']);
            $sheets[] = new FuelConsumptionDetailsSheet($this->data['details']);
        }

        return $sheets;
    }
}

class ReportSummarySheet implements FromCollection, WithHeadings, ShouldAutoSize, WithTitle
{
    protected $summary;
    protected $date;

    public function __construct($summary, $date)
    {
        $this->summary = $summary;
        $this->date = $date;
    }

    public function title(): string
    {
        return 'Summary';
    }

    public function headings(): array
    {
        return [
            'Vehicle',
            'Total Trips',
            'Start Time',
            'End Time',
            'Total Distance',
            'Total Duration',
            'Total Fuel',
            'Total Stops',
            'Date'
        ];
    }

    public function collection()
    {
        return collect([
            [
                $this->summary['vehicle'],
                $this->summary['total_trips'],
                $this->summary['start_time'],
                $this->summary['end_time'],
                $this->summary['total_distance'],
                $this->summary['total_duration'],
                $this->summary['total_fuel'],
                $this->summary['total_stops'],
                $this->date
            ]
        ]);
    }
}

class GeofenceEventsSheet implements FromCollection, WithHeadings, ShouldAutoSize, WithTitle
{
    protected $events;

    public function __construct($events)
    {
        $this->events = $events;
    }

    public function title(): string
    {
        return 'Geofence Events';
    }

    public function headings(): array
    {
        return [
            'Event Type',
            'Geofence',
            'Time',
            'Location'
        ];
    }

    public function collection()
    {
        return collect($this->events)->map(function ($event) {
            return [
                $event['type'] === 'geofence_exit_event' ? 'Exit' : 'Entry',
                $event['geofence'],
                $event['time'],
                $event['location']
            ];
        });
    }
}

class ReportDetailsSheet implements FromCollection, WithHeadings, ShouldAutoSize, WithTitle
{
    protected $data;
    protected $reportingType;

    public function __construct($data, $reportingType)
    {
        $this->data = $data;
        $this->reportingType = $reportingType;
    }

    public function title(): string
    {
        return 'Details';
    }

    public function headings(): array
    {
        if ($this->reportingType == 'routes') {
            return [
                'Time',
                'Type',
                'Duration',
                'Location',
                'Distance',
                'Fuel Consumption'
            ];
        } else {
            return [
                'Vehicle',
                'Alarm Type',
                'Triggered At',
                'Geofence',
                'Location'
            ];
        }
    }

    public function collection()
    {
        if ($this->reportingType == 'routes') {
            return collect($this->data)->map(function ($item) {
                $timeRange = $item['start_time'] . '-' . $item['end_time'];

                if ($item['type'] == 'stop') {
                    return [
                        $timeRange,
                        ucfirst($item['type']),
                        $item['duration'],
                        $item['location'],
                        $item['stop_distance'] ?? '-',
                        $item['stop_fuel'] ?? '-'
                    ];
                } else { // Trip
                    $location = 'From: ' . ($item['start_location'] ?? '-') . ' To: ' . ($item['end_location'] ?? '-');
                    return [
                        $timeRange,
                        ucfirst($item['type']),
                        $item['duration'],
                        $location,
                        $item['trip_distance'] ?? '-',
                        $item['trip_fuel'] ?? '-'
                    ];
                }
            });
        }

        return collect($this->data);
    }
}

class FuelConsumptionSummarySheet implements FromCollection, WithHeadings, ShouldAutoSize, WithTitle
{
    protected $summary;

    public function __construct($summary)
    {
        $this->summary = $summary;
    }

    public function title(): string
    {
        return 'Fuel Summary';
    }

    public function headings(): array
    {
        return [
            'Vehicle',
            'Date',
            'Total Fuel Used (L)',
            'Total Distance (km)',
            'Start Time',
            'End Time',
            'Total Trips',
            'Total Stops',
            'Trip Duration',
            'Stop Duration'
        ];
    }

    public function collection()
    {
        return collect([
            [
                $this->summary['vehicle'],
                $this->summary['date'],
                $this->summary['total_fuel_used'],
                $this->summary['total_distance'],
                $this->summary['start_time'],
                $this->summary['end_time'],
                $this->summary['total_trips'],
                $this->summary['total_stops'],
                $this->summary['total_trip_duration'],
                $this->summary['total_stop_duration']
            ]
        ]);
    }
}

class FuelConsumptionDetailsSheet implements FromCollection, WithHeadings, ShouldAutoSize, WithTitle
{
    protected $details;

    public function __construct($details)
    {
        $this->details = $details;
    }

    public function title(): string
    {
        return 'Fuel Details';
    }

    public function headings(): array
    {
        return [
            'Time',
            'Type',
            'Duration',
            'Location',
            'Distance',
            'Fuel Consumption'
        ];
    }

    public function collection()
    {
        return collect($this->details)->map(function ($item) {
            $timeRange = $item['start_time'] . '-' . $item['end_time'];

            if ($item['type'] == 'stop') {
                return [
                    $timeRange,
                    ucfirst($item['type']),
                    $item['duration'],
                    $item['location'],
                    $item['trip_distance'] ?? '-',
                    $item['trip_fuel'] ?? '-'
                ];
            } else { // Trip
                $location = 'From: ' . ($item['start_location'] ?? '-') . ' To: ' . ($item['end_location'] ?? '-');
                return [
                    $timeRange,
                    ucfirst($item['type']),
                    $item['duration'],
                    $location,
                    $item['trip_distance'] ?? '-',
                    $item['trip_fuel'] ?? '-'
                ];
            }
        });
    }
}
