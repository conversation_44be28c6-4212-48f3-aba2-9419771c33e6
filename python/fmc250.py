import socket
import json
import requests
import os
import datetime
import struct
import decimal
import threading
from shapely.geometry import Point, Polygon
from geopy.distance import geodesic
from command import create_codec12_command, decode_command_response  # Import your command script
import time
import pytz
from math import radians, sin, cos, sqrt, atan2
from collections import defaultdict
from queue import PriorityQueue
import heapq
import fcntl  # Add this import at the top

HOST = '0.0.0.0'  #function may not work in Linux systems, change to string with IP adress example: "***********"
PORT = 2021  #change this to your port

BASE_PATH = "/home/<USER>/ControlOne/public_html/public/"
COMMAND_QUEUE_PATH = BASE_PATH + "command/queue.json"
PLATFORM_BASE_URL = "https://piattaforma.controllone.it/"
LARAVEL_COMMAND_API_URL = PLATFORM_BASE_URL + "api/save-command-response"

# Constants for route progress
ROUTE_FOLDER = BASE_PATH + "routes"
UPDATE_ROUTE_API_URL = PLATFORM_BASE_URL + "api/update-route" 
COMPLETE_ROUTE_API = PLATFORM_BASE_URL + "api/complete-route"
RADIUS_THRESHOLD = 500

# Constants for queue management
MAX_QUEUE_SIZE = 1000  # Prevent unbounded growth
MAX_PROCESSING_DELAY = 50  # Maximum seconds to wait for ordering
CLEANUP_INTERVAL = 600  # Cleanup old queues every 10 minutes

queue_lock = threading.Lock()
COMMAND_COOLDOWN = 5  # seconds

class DeviceMessageHandler:
    def __init__(self):
        self.device_queues = defaultdict(lambda: PriorityQueue(maxsize=MAX_QUEUE_SIZE))
        self.device_locks = defaultdict(threading.Lock)
        self.last_activity = defaultdict(float)
        self._cleanup_thread = threading.Thread(target=self._cleanup_old_queues, daemon=True)
        self._cleanup_thread.start()
        
    def clear_all_queues(self):
        """Clear all device queues"""
        with threading.Lock():
            for imei in list(self.device_queues.keys()):
                while not self.device_queues[imei].empty():
                    try:
                        self.device_queues[imei].get_nowait()
                    except queue.Empty:
                        break
            self.last_activity.clear()

    def _cleanup_old_queues(self):
        while True:
            current_time = time.time()
            with threading.Lock():
                for imei in list(self.last_activity.keys()):
                    if current_time - self.last_activity[imei] > CLEANUP_INTERVAL:
                        del self.device_queues[imei]
                        del self.device_locks[imei]
                        del self.last_activity[imei]
            time.sleep(CLEANUP_INTERVAL)

    def process_message(self, device_imei, data, conn, addr):
        try:
            timestamp = extract_timestamp_from_data(data.hex())
            current_time = time.time()
            
            # Skip very old messages (e.g., older than 1 hour)
            if (current_time - (timestamp / 1000)) > 3600:  # 3600 seconds = 1 hour
                print(f"Skipping old message from {device_imei}, age: {(current_time - (timestamp / 1000))/3600:.2f} hours")
                return True
            
            with self.device_locks[device_imei]:
                self.last_activity[device_imei] = current_time
                
                # Add to device queue with priority based on timestamp
                try:
                    self.device_queues[device_imei].put_nowait((-timestamp, data))
                except queue.Full:
                    # If queue is full, process oldest message first
                    self._process_oldest_message(device_imei, conn)
                    self.device_queues[device_imei].put((-timestamp, data))
                
                # Process messages that are ready
                self._process_ready_messages(device_imei, conn, current_time)
                
            return True
        except Exception as e:
            print(f"Error processing message for device {device_imei}: {e}")
            return False

    def _process_oldest_message(self, device_imei, conn):
        """Process the oldest message in the queue."""
        try:
            _, data = self.device_queues[device_imei].get_nowait()
            self._process_single_message(device_imei, data, conn)
        except queue.Empty:
            pass

    def _process_ready_messages(self, device_imei, conn, current_time):
        """Process all messages that are ready to be processed."""
        messages_processed = 0
        while not self.device_queues[device_imei].empty():
            try:
                neg_timestamp, data = self.device_queues[device_imei].queue[0]
                timestamp = -neg_timestamp
                
                # Check if message is ready to be processed
                time_diff = current_time - (timestamp / 1000)  # Convert to seconds
                if time_diff > MAX_PROCESSING_DELAY:
                    # Process old message
                    self.device_queues[device_imei].get_nowait()
                    self._process_single_message(device_imei, data, conn)
                    messages_processed += 1
                elif messages_processed > 0:
                    # If we've processed some messages but hit a new one, process it
                    self.device_queues[device_imei].get_nowait()
                    self._process_single_message(device_imei, data, conn)
                else:
                    # Wait for more messages
                    break
            except queue.Empty:
                break

    def _process_single_message(self, device_imei, data, conn):
        """Process a single message with existing logic."""
        try:
            record_number = codec_parser_trigger(data.hex(), device_imei, "SERVER")
            record_response = (record_number).to_bytes(4, byteorder="big")
            conn.sendall(record_response)
        except Exception as e:
            print(f"Error processing message for device {device_imei}: {e}")

# Create global handler instance
message_handler = DeviceMessageHandler()

def handle_client(conn, addr):
    """
    Handles the connection with each client individually in a thread.
    """
    print(f"Connected by {addr}")
    device_imei = "default_IMEI"
    conn.settimeout(5)

    while True:
        last_command_time = 0
        try:
            data = conn.recv(10240)  # receive data from the client
            # print(f"Data received from {addr}: {data.hex()}")

            if not data:  # if no data, break out of the loop
                break

            elif imei_checker(data.hex()):
                device_imei = ascii_imei_converter(data.hex())
                conn.sendall((1).to_bytes(1, byteorder="big"))
                print(f"IMEI received {device_imei}")
                # print(f"IMEI response sent to {addr}")

            elif codec_8e_checker(data.hex().replace(" ", "")):
                # Handle data with message handler
                message_handler.process_message(device_imei, data, conn, addr)

                # Command handling remains unchanged
                current_time = time.time()
                if current_time - last_command_time < COMMAND_COOLDOWN:
                    time.sleep(COMMAND_COOLDOWN - (current_time - last_command_time))

                # Fetch the next command for the IMEI
                command_data, queue_data = fetch_next_command(device_imei)
                if not command_data:
                    # print(f"No commands in queue for IMEI {device_imei}")
                    continue

                command = command_data["command"]
                if not command:
                    print(f"Command data for empty :) {device_imei}")
                    continue
                encoded_command = create_codec12_command(command)
                print(f"Sending command to device: {command}")
                conn.sendall(encoded_command)  # Send encoded command'
				# Save updated queue data (remove the sent command)
                save_queue(queue_data)

                # Wait for and decode the response
                response_data = conn.recv(10240)
                print(f"Response received: {response_data.hex()}")
                decoded_response = decode_command_response(response_data.hex())
                print(f"Decoded Response: {decoded_response.response}")
				
                if decoded_response:
                	# Send the payload to Laravel API
                    send_to_laravel_api(device_imei, command, decoded_response.response)
				
                last_command_time = time.time()

            else:
                print(f"No expected data received from {addr} - dropping connection")
                break

        except socket.timeout:
            print(f"Connection timed out with {addr}")
            break

    conn.close()  # close the connection after finishing with the client


def input_trigger():
	print("Paste full 'Codec 8' packet to parse it or:")
	print("Type SERVER to start the server or:")
	print("Type EXIT to stop the program")
	device_imei = "default_IMEI"
	user_input = input("waiting for input: ")
	if user_input.upper() == "EXIT":
		print(f"exiting program............")
		exit()	

	elif user_input.upper() == "SERVER":
		start_server_trigger()
	else:		
		try:
			if codec_8e_checker(user_input.replace(" ","")) == False:
				print("Wrong input or invalid Codec8 packet")
				print()
				input_trigger()
			else:
				codec_parser_trigger(user_input, device_imei, "USER")
		except Exception as e:
			print(f"error occured: {e} enter proper Codec8 packet or EXIT!!!")
			input_trigger()		

def start_server_trigger():
    print("Starting server!")
    # Clear all queues on startup
    message_handler.clear_all_queues()
    
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind((HOST, PORT))
        s.listen()  # listen for incoming connections
        print(f"Server listening on {HOST}:{PORT}")

        while True:
            conn, addr = s.accept()  # accept a new client connection
            # Start a new thread to handle each client
            client_thread = threading.Thread(target=handle_client, args=(conn, addr))
            client_thread.start()  # start the thread for handling the client

####################################################
###############__CRC16/ARC Checker__################
####################################################

def crc16_arc(data):    
	data_part_length_crc = int(data[8:16], 16)
	data_part_for_crc = bytes.fromhex(data[16:16+2*data_part_length_crc])
	crc16_arc_from_record = data[16+len(data_part_for_crc.hex()):24+len(data_part_for_crc.hex())]  
	
	crc = 0
	
	for byte in data_part_for_crc:
		crc ^= byte
		for _ in range(8):
			if crc & 1:
				crc = (crc >> 1) ^ 0xA001
			else:
				crc >>= 1
	
	if crc16_arc_from_record.upper() == crc.to_bytes(4, byteorder='big').hex().upper():
		print ("CRC check passed!")
		print (f"Record length: {len(data)} characters // {int(len(data)/2)} bytes")
		return True
	else:
		print("CRC check Failed!")
		return False

####################################################

def codec_8e_checker(codec8_packet):
	if str(codec8_packet[16:16+2]).upper() != "8E" and str(codec8_packet[16:16+2]).upper() != "08":	
		print()	
		print(f"Invalid packet!!!!!!!!!!!!!!!!!!!")		
		return False
	else:
		return crc16_arc(codec8_packet)

def codec_parser_trigger(codec8_packet, device_imei, props):
		try:			
			return codec_8e_parser(codec8_packet.replace(" ",""), device_imei, props)

		except Exception as e:
			print(f"Error occured: {e} enter proper Codec8 packet or EXIT!!!")
			input_trigger()

def imei_checker(hex_imei):
    try:
        ascii_imei = bytes.fromhex(hex_imei[4:]).decode()
        return len(ascii_imei) == 15
    except:
        return False

def ascii_imei_converter(hex_imei):
	return bytes.fromhex(hex_imei[4:]).decode()

def extract_timestamp_from_data(hex_data):
    """
    Extract timestamp from the codec 8E packet.
    Returns timestamp as integer for ordering.
    """
    try:
        # Adjust these indices based on your packet structure
        timestamp_hex = hex_data[20:36]  # Assuming timestamp is at this position
        return int(timestamp_hex, 16)
    except Exception as e:
        print(f"Error extracting timestamp: {e}")
        return int(time.time() * 1000)  # Fallback to current time

					
# gprs command queue & response handling
def fetch_next_command(imei):
    """
    Fetches the next command for the given IMEI from the queue.json file.
    """
    with queue_lock:  # Ensure thread-safe access
        try:
            with open(COMMAND_QUEUE_PATH, "r") as file:
                command_data = json.load(file)

            if imei in command_data and command_data[imei]:
                # Pop the first command from the queue for the given IMEI
                command_to_send = command_data[imei].pop(0)
                return command_to_send, command_data
            return None, command_data
        except FileNotFoundError:
            print("Command queue file not found.")
            return None, {}
        except json.JSONDecodeError:
            print("Error decoding JSON file.")
            return None, {}

def save_queue(command_data):
    """
    Saves the updated queue back to the JSON file.
    """
    with queue_lock:  # Ensure thread-safe access
        with open(COMMAND_QUEUE_PATH, "w") as file:
            json.dump(command_data, file, indent=4)

def send_to_laravel_api(imei, command, response):
    """
    Sends the decoded response to the Laravel API.
    """
    payload = {
        "imei": imei,
        "command": command,
        "response": response.decode('utf-8', errors='ignore')
    }
    try:
        response = requests.post(LARAVEL_COMMAND_API_URL, json=payload)
        if response.status_code == 200:
            print("Data successfully sent to Laravel API.")
        else:
            print(f"Failed to send data to Laravel API: {response.text}")
    except Exception as e:
        print(f"Error sending data to Laravel API: {e}")

####################################################
###############_Codec8E_parser_code_################
####################################################

def codec_8e_parser(codec_8E_packet, device_imei, props): #think a lot before modifying  this function
	# print()
#	print (str("codec 8 string entered - " + codec_8E_packet))

# 	io_dict_raw = {}
# 	timestamp = codec_8E_packet[20:36]	
# 	io_dict_raw["device_IMEI"] = device_imei
# 	io_dict_raw["last_update"] = device_time_stamper(timestamp)
# #	io_dict_raw["_timestamp_"] = device_time_stamper(timestamp)
# #	io_dict_raw["_rec_delay_"] = record_delay_counter(timestamp)
# 	io_dict_raw["data_length"] = "Record length: " + str(int(len(codec_8E_packet))) + " characters" + " // " + str(int(len(codec_8E_packet) // 2)) + " bytes"
# 	io_dict_raw["_raw_data__"] = codec_8E_packet

	# try: #writing raw DATA dictionary to ./data/data.json
	# 	json_printer_rawDATA(io_dict_raw, device_imei)
	# except Exception as e:
	# 	print(f"JSON raw data writing error occured = {e}")

	zero_bytes = codec_8E_packet[:8]
	# print()
	# print (str("zero bytes = " + zero_bytes))

	data_field_length = int(codec_8E_packet[8:8+8], 16)
	# print (f"data field lenght = {data_field_length} bytes")
	codec_type = str(codec_8E_packet[16:16+2])
	# print (f"codec type = {codec_type}")

	data_step = 4
	if codec_type == "08":
		data_step = 2
	else:
		pass

	number_of_records = int(codec_8E_packet[18:18+2], 16)
	# print (f"number of records = {number_of_records}")

	record_number = 1
	avl_data_start = codec_8E_packet[20:]
	data_field_position = 0
	while data_field_position < (2*data_field_length-6):				
		io_dict = {}
		io_dict["device_IMEI"] = device_imei		
		# print()
		# print (f"data from record {record_number}")	
		# print (f"########################################")

		timestamp = avl_data_start[data_field_position:data_field_position+16]
		io_dict["last_update"] = device_time_stamper(timestamp)
		io_dict["_timestamp_"] = time_stamper_for_json()
		# print (f"timestamp = {device_time_stamper(timestamp)}")	
		io_dict["_rec_delay_"] = record_delay_counter(timestamp)		
		data_field_position += len(timestamp)

		priority = avl_data_start[data_field_position:data_field_position+2]
		io_dict["priority"] = int(priority, 16)
		# print (f"record priority = {int(priority, 16)}")
		data_field_position += len(priority)

		longitude = avl_data_start[data_field_position:data_field_position+8]
	#	io_dict["longitude"] = struct.unpack('>i', bytes.fromhex(longitude))[0]
	#	print (f"longitude = {struct.unpack('>i', bytes.fromhex(longitude))[0]}")
		io_dict["longitude"] = coordinate_formater(longitude)
		# print (f"longitude = {coordinate_formater(longitude)}")
		data_field_position += len(longitude)

		latitude = avl_data_start[data_field_position:data_field_position+8]
	#	print (f"latitude = {struct.unpack('>i', bytes.fromhex(latitude))[0]}")
	#	io_dict["latitude"] = struct.unpack('>i', bytes.fromhex(latitude))[0]
		io_dict["latitude"] = coordinate_formater(latitude)
		# print (f"latitude = {coordinate_formater(latitude)}")
		data_field_position += len(latitude)

		altitude = avl_data_start[data_field_position:data_field_position+4]
		# print(f"altitude = {int(altitude, 16)}")
		io_dict["altitude"] = int(altitude, 16)
		data_field_position += len(altitude)

		angle = avl_data_start[data_field_position:data_field_position+4]
		# print(f"angle = {int(angle, 16)}")
		io_dict["angle"] = int(angle, 16)
		data_field_position += len(angle)

		satelites = avl_data_start[data_field_position:data_field_position+2]
		# print(f"satelites = {int(satelites, 16)}")
		io_dict["satelites"] = int(satelites, 16)
		data_field_position += len(satelites)

		speed = avl_data_start[data_field_position:data_field_position+4]
		io_dict["speed"] = int(speed, 16)
		# print(f"speed = {int(speed, 16)}")
		data_field_position += len(speed)

		# Parse the event ID
		event_io_id = avl_data_start[data_field_position:data_field_position+data_step]
		event_id = int(event_io_id, 16)
		io_dict["eventID"] = int(event_io_id, 16)
		# print(f"event ID = {int(event_io_id, 16)}")
		data_field_position += len(event_io_id)

		total_io_elements = avl_data_start[data_field_position:data_field_position+data_step]
		total_io_elements_parsed = int(total_io_elements, 16)
		# print(f"total I/O elements in record {record_number} = {total_io_elements_parsed}")
		data_field_position += len(total_io_elements)

		byte1_io_number = avl_data_start[data_field_position:data_field_position+data_step]
		byte1_io_number_parsed = int(byte1_io_number, 16)
		# print(f"1 byte io count = {byte1_io_number_parsed}")
		data_field_position += len(byte1_io_number)		

		if byte1_io_number_parsed > 0:
			i = 1				
			while i <= byte1_io_number_parsed:
				key = avl_data_start[data_field_position:data_field_position+data_step]
				data_field_position += len(key)
				value = avl_data_start[data_field_position:data_field_position+2]

				io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
				data_field_position += len(value)
				# print (f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				i += 1
		else:
			pass

		byte2_io_number = avl_data_start[data_field_position:data_field_position+data_step]
		byte2_io_number_parsed = int(byte2_io_number, 16)
		# print(f"2 byte io count = {byte2_io_number_parsed}")
		data_field_position += len(byte2_io_number)

		if byte2_io_number_parsed > 0:
			i = 1
			while i <= byte2_io_number_parsed:
				key = avl_data_start[data_field_position:data_field_position+data_step]
				data_field_position += len(key)

				value = avl_data_start[data_field_position:data_field_position+4]
				io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
				data_field_position += len(value)
				# print (f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				i += 1
		else:
			pass

		byte4_io_number = avl_data_start[data_field_position:data_field_position+data_step]
		byte4_io_number_parsed = int(byte4_io_number, 16)
		# print(f"4 byte io count = {byte4_io_number_parsed}")
		data_field_position += len(byte4_io_number)

		if byte4_io_number_parsed > 0:
			i = 1
			while i <= byte4_io_number_parsed:
				key = avl_data_start[data_field_position:data_field_position+data_step]
				data_field_position += len(key)

				value = avl_data_start[data_field_position:data_field_position+8]
				io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
				data_field_position += len(value)
				# print(f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				i += 1
		else:
			pass

		byte8_io_number = avl_data_start[data_field_position:data_field_position+data_step]
		byte8_io_number_parsed = int(byte8_io_number, 16)
		# print(f"8 byte io count = {byte8_io_number_parsed}")
		data_field_position += len(byte8_io_number)

		if byte8_io_number_parsed > 0:
			i = 1
			while i <= byte8_io_number_parsed:
				key = avl_data_start[data_field_position:data_field_position+data_step]
				data_field_position += len(key)

				value = avl_data_start[data_field_position:data_field_position+16]
				io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
				data_field_position += len(value)
				# print(f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				i += 1
		else:
			pass

		if codec_type.upper() == "8E":

			byteX_io_number = avl_data_start[data_field_position:data_field_position+4]
			byteX_io_number_parsed = int(byteX_io_number, 16)
			# print(f"X byte io count = {byteX_io_number_parsed}")
			data_field_position += len(byteX_io_number)

			if byteX_io_number_parsed > 0:
				i = 1
				while i <= byteX_io_number_parsed:
					key = avl_data_start[data_field_position:data_field_position+4]
					data_field_position += len(key)

					value_length = avl_data_start[data_field_position:data_field_position+4]
					data_field_position += 4
					value = avl_data_start[data_field_position:data_field_position+(2*(int(value_length, 16)))]
					io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)		
					data_field_position += len(value)
					# print(f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				#	print (f"data field postition = {data_field_position}")
				#	print (f"data_field_length = {2*data_field_length}")
					i += 1
			else:
				pass
		else:
			pass

		record_number += 1
		
		try: #writing dictionary to ./data/data.json
			json_printer(io_dict, device_imei)
		except Exception as e:
			print(f"JSON writing error occured = {e}")
                  
	    # Format coordinates
		latitude = coordinate_formater(latitude)
		longitude = coordinate_formater(longitude)
		current_point = Point(float(longitude), float(latitude))

		# Construct the geofence file path
		geofence_file = f"{BASE_PATH}geofences/{device_imei}.json"

		# Check if the geofence file exists
		if os.path.exists(geofence_file):
			try:
				# Load geofence data
				with open(geofence_file, "r") as file:
					geofences = json.load(file)

				# print(f"Checking geofences {geofences}")

				# Process each geofence
				for geofence in geofences:
					geofence_data = geofence["geofence"]
					inside_geofence = is_point_in_geofence(current_point, geofence_data)

					new_state = "in" if inside_geofence else "out"
					prev_state, new_state = update_geofence_state(device_imei, new_state)

					# print(f"Checking states {new_state}, {prev_state}")

					if prev_state == "in" and new_state == "out":
						# Log geofence exit event
						log_geofence_event_to_backend(device_imei, {
							"longitude": longitude,
							"latitude": latitude,
							"geofence_id": geofence["id"],
							"type": "geofence_exit"
						})

					elif prev_state == "out" and new_state == "in":
						# Log geofence entry event
						log_geofence_event_to_backend(device_imei, {
							"longitude": longitude,
							"latitude": latitude,
							"geofence_id": geofence["id"],
							"type": "geofence_entry"
						})

			except json.JSONDecodeError:
				print(f"Invalid JSON in geofence file for device {device_imei}. Skipping geofence processing.")
			except Exception as e:
				print(f"Unexpected error while processing geofence for device {device_imei}: {e}")

					
	
		# update_route_progress(device_imei, latitude, longitude, io_dict)

		if event_id != 0 and (event_id == 249 or event_id == 247 or event_id == 246 or event_id == 252 or event_id == 318):
			send_event_to_api(device_imei, io_dict)



	if props == "SERVER":	

		total_records_parsed = int(avl_data_start[data_field_position:data_field_position+2], 16)
		# print()
		# print(f"total parsed records = {total_records_parsed}")
		# print()
		return int(number_of_records)
	
	else:
		total_records_parsed = int(avl_data_start[data_field_position:data_field_position+2], 16)
		# print()
		# print(f"total parsed records = {total_records_parsed}")
		# print()


		input_trigger()

####################################################
###############_End_of_MAIN_Parser_Code#############
####################################################

####################################################
###############_Coordinates_Function_###############
####################################################

def coordinate_formater(hex_coordinate): #may return too large longitude need to test this more
	coordinate = int(hex_coordinate, 16)
	dec_coordinate = coordinate / 10000000
	if coordinate & (1 << 31):
		dec_coordinate *= -1
	else:
		pass
	return dec_coordinate



####################################################
###############____JSON_Functions____###############
####################################################

def parse_custom_datetime(date_str):
    try:
        return datetime.datetime.strptime(date_str.split(" (")[0], "%H:%M:%S %d-%m-%Y")
    except ValueError:
        return None

class FileLock:
    def __init__(self, file_path):
        self.file_path = file_path
        self.lock_path = f"{file_path}.lock"
        
    def __enter__(self):
        self.lock_file = open(self.lock_path, 'w')
        fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_EX)
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_UN)
        self.lock_file.close()

# printing device data to JSON file
def json_printer(io_dict, device_imei):
    # Update live data
    update_live_data(io_dict, device_imei)
    
    # Store historical data
    store_historical_data(io_dict, device_imei)

def sanitize_dict(input_dict):
    """
    Deep sanitize the input dictionary to handle malformed JSON data.
    """
    if not isinstance(input_dict, dict):
        return {}

    sanitized = {}
    for key, value in input_dict.items():
        try:
            # Convert key to string if it's not already
            str_key = str(key)
            
            # Handle different value types
            if isinstance(value, dict):
                sanitized[str_key] = sanitize_dict(value)
            elif isinstance(value, (list, tuple)):
                sanitized[str_key] = [sanitize_dict(item) if isinstance(item, dict) else item 
                                    for item in value if item is not None]
            elif value is not None:
                # Convert value to string and back to ensure JSON compatibility
                json.dumps(value)  # This will raise an error if value is not JSON serializable
                sanitized[str_key] = value
        except (TypeError, ValueError, json.JSONDecodeError):
            # If value is not JSON serializable, convert to string
            try:
                sanitized[str_key] = str(value)
            except:
                continue
    
    return sanitized

def repair_json_file(file_path, expect_list=False):
    """
    Attempt to repair corrupted JSON file by removing incomplete entries.
    :param file_path: Path to the JSON file
    :param expect_list: If True, ensures return value is a list
    :return: Repaired data structure (list or dict depending on expect_list)
    """
    try:
        with open(file_path, 'r') as file:
            content = file.read().strip()
            if not content:
                return [] if expect_list else {}

            # Handle list-type JSON files
            if content.startswith('['):
                depth = 0
                last_complete = 0
                in_string = False
                escape = False
                
                for i, char in enumerate(content):
                    if not in_string:
                        if char == '[':
                            depth += 1
                        elif char == ']':
                            depth -= 1
                            if depth == 0:
                                last_complete = i + 1
                        elif char == '"':
                            in_string = True
                    else:
                        if char == '\\':
                            escape = not escape
                        elif char == '"' and not escape:
                            in_string = False
                        else:
                            escape = False
                
                # Extract the valid portion of the JSON
                valid_content = content[:last_complete]
                if not valid_content or valid_content == '[':
                    return []
                
                try:
                    return json.loads(valid_content)
                except json.JSONDecodeError:
                    return []

            # Handle dict-type JSON files
            else:
                depth = 0
                last_complete = 0
                in_string = False
                escape = False
                
                for i, char in enumerate(content):
                    if not in_string:
                        if char == '{':
                            depth += 1
                        elif char == '}':
                            depth -= 1
                            if depth == 0:
                                last_complete = i + 1
                        elif char == '"':
                            in_string = True
                    else:
                        if char == '\\':
                            escape = not escape
                        elif char == '"' and not escape:
                            in_string = False
                        else:
                            escape = False
                
                # Extract the valid portion of the JSON
                valid_content = content[:last_complete]
                if not valid_content or valid_content == '{':
                    return [] if expect_list else {}
                
                try:
                    result = json.loads(valid_content)
                    return [] if expect_list else result
                except json.JSONDecodeError:
                    return [] if expect_list else {}
            
    except Exception as e:
        print(f"Error repairing JSON file: {e}")
        return [] if expect_list else {}

def update_live_data(io_dict, device_imei):
    live_data_path = BASE_PATH + "data"
    live_data_file = "live.json"
    file_path = os.path.join(live_data_path, live_data_file)
    temp_file_path = file_path + ".tmp"

    # Ensure the directory exists
    os.makedirs(live_data_path, exist_ok=True)

    # Use file-based locking instead of threading lock
    with FileLock(file_path):
        # Read existing data with recovery mechanism
        live_data = {}
        if os.path.exists(file_path):
            try:
                with open(file_path, "r") as file:
                    live_data = json.load(file)
            except json.JSONDecodeError:
                print("JSON decode error, attempting repair...")
                live_data = repair_json_file(file_path)

        # Update data
        if device_imei not in live_data:
            live_data[device_imei] = {}
        live_data[device_imei].update(sanitize_dict(io_dict))

        # Write atomically
        try:
            with open(temp_file_path, "w") as temp_file:
                json_str = json.dumps(live_data, ensure_ascii=False, separators=(',', ':'))
                temp_file.write(json_str)
                temp_file.flush()
                os.fsync(temp_file.fileno())
            os.replace(temp_file_path, file_path)
        except Exception as e:
            print(f"Error writing JSON data: {e}")
            if os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                except OSError:
                    pass
            raise

def store_historical_data(io_dict, device_imei):
    history_path = f"{BASE_PATH}data/history/{device_imei}"
    date_file = "dates.json"
    
    italy_timezone = pytz.timezone('Europe/Rome')
    current_date = datetime.datetime.now(italy_timezone).strftime('%d-%m-%Y')
    data_file = f"{current_date}.json"
    
    if not os.path.exists(history_path):
        os.makedirs(history_path)
    
    date_path = os.path.join(history_path, date_file)
    data_path = os.path.join(history_path, data_file)
    
    # Handle dates file with locking
    with FileLock(date_path):
        dates = set()
        if os.path.exists(date_path):
            try:
                with open(date_path, "r") as file:
                    dates = set(json.load(file))
            except json.JSONDecodeError:
                print("Dates file corrupted, attempting repair...")
                dates = set()
        
        dates.add(current_date)
        sorted_dates = sorted(dates, key=lambda d: datetime.datetime.strptime(d, '%d-%m-%Y'), reverse=True)
        
        with open(date_path, "w") as file:
            json.dump(sorted_dates, file, separators=(',', ':'))

    # Handle daily data file with locking
    with FileLock(data_path):
        daily_data = []
        if os.path.exists(data_path):
            try:
                with open(data_path, "r") as file:
                    content = file.read().strip()
                    if content:
                        daily_data = json.loads(content)
            except json.JSONDecodeError:
                daily_data = repair_json_file(data_path, expect_list=True)
        
        sanitized_io_dict = sanitize_dict(io_dict)
        if sanitized_io_dict and (not daily_data or daily_data[-1] != sanitized_io_dict):
            daily_data.append(sanitized_io_dict)
            
            with open(data_path, "w") as file:
                json.dump(daily_data, file, separators=(',', ':'))



# Logging device events :)
# Define the Laravel API endpoint
LARAVEL_EVENT_API_URL = PLATFORM_BASE_URL + "api/log-device-event"

def send_event_to_api(imei, io_data):
    """
    Sends event data to the Laravel API.
    :param imei: Device IMEI
    :param io_data: Parsed IO data
    """
    try:
        payload = {
            "imei": imei,
            "data": io_data
        }
        headers = {"Content-Type": "application/json"}
        response = requests.post(LARAVEL_EVENT_API_URL, data=json.dumps(payload), headers=headers)

        if response.status_code == 200:
            print("Event successfully sent to Laravel API.")
        else:
            print(f"Failed to send event. Status Code: {response.status_code}, Response: {response.text}")
    except Exception as e:
        print(f"Error sending event to Laravel API: {e}")


# geofence checker
def is_point_in_geofence(current_point, geofence_data):
    """
    Check if the given point is inside the geofence.
    Supports polygon, circle, and rectangle geofences.
    """
    if geofence_data["type"] == "polygon":  
        # Extract polygon coordinates and create Polygon object
        polygon_coords = [(g["lng"], g["lat"]) for g in geofence_data["geofence"]]
        polygon = Polygon(polygon_coords)
        return polygon.contains(current_point)

    elif geofence_data["type"] == "circle":  
        # Extract circle center and radius
        center_lat = geofence_data["geofence"]["lat"]
        center_lon = geofence_data["geofence"]["lng"]
        radius = geofence_data["radius"]

        center_point = (center_lat, center_lon)
        distance = geodesic((current_point.y, current_point.x), center_point).meters
        return distance <= radius

    elif geofence_data["type"] == "rectangle":  
        # Extract rectangle bounds
        south = geofence_data["geofence"]["south"]
        west = geofence_data["geofence"]["west"]
        north = geofence_data["geofence"]["north"]
        east = geofence_data["geofence"]["east"]

        return (south <= current_point.y <= north) and (west <= current_point.x <= east)

    return False  # Invalid geofence type


def update_geofence_state(imei, new_state):
    print("Getting old geofence")
    file_path = os.path.join(BASE_PATH, "geofence_states/states.json")

    # Ensure the directory exists
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # Ensure the file exists
    if not os.path.exists(file_path):
        with open(file_path, "w") as file:
            json.dump({}, file)

    try:
        with open(file_path, "r") as file:
            content = file.read()
            geofence_data = json.loads(content) if content.strip() else {}
    except (FileNotFoundError, json.JSONDecodeError):
        print("Invalid JSON or file missing. Reinitializing.")
        geofence_data = {}

    # Get the previous state, default to "out"
    previous_state = geofence_data.get(imei, {}).get("state", "out")
    
    # Always update the geofence data
    geofence_data[imei] = {"state": new_state}

    # Write the updated state back to the file
    with open(file_path, "w") as file:
        json.dump(geofence_data, file, indent=4)

    return previous_state, new_state

def log_geofence_event_to_backend(imei, event_data):
    """Send geofence exit event to Laravel backend."""
    api_url = PLATFORM_BASE_URL + "api/log-geofence-event"
    payload = {"imei": imei, **event_data}
    response = requests.post(api_url, json=payload)
    print(f"Logged event for IMEI {imei}, Response: {response.status_code}")

####################################################
###############____TIME_FUNCTIONS____###############
####################################################

def time_stamper():
	current_server_time = datetime.datetime.now()	
	server_time_stamp = current_server_time.strftime('%H:%M:%S %d-%m-%Y')
	return server_time_stamp

def device_time_stamper(timestamp):
    # Convert the hex timestamp to milliseconds and then to seconds
    timestamp_ms = int(timestamp, 16) / 1000
    
    # Use timezone-aware datetime for UTC
    timestamp_utc = datetime.datetime.fromtimestamp(timestamp_ms, datetime.timezone.utc)
    
    # Define the timezone for Italy
    italy_timezone = pytz.timezone('Europe/Rome')
    
    # Convert UTC to Italy timezone
    timestamp_italy = timestamp_utc.astimezone(italy_timezone)
    
    # Format both timestamps
    formatted_timestamp_italy = timestamp_italy.strftime("%d/%m/%Y %H:%M")  # Italian format
    
    # Combine both formatted timestamps
    formatted_timestamp = f"{formatted_timestamp_italy}"
    
    return formatted_timestamp

def time_stamper_for_json():
    # Define the timezone for Italy
    italy_timezone = pytz.timezone('Europe/Rome')
    
    # Get the current time in Italy's timezone
    current_italy_time = datetime.datetime.now(italy_timezone)
    
    # Format the time in the desired Italian format
    italy_time_stamp = current_italy_time.strftime('%d/%m/%Y %H:%M')
    
    return italy_time_stamp


def record_delay_counter(timestamp):
	timestamp_ms = int(timestamp, 16) / 1000
	current_server_time = datetime.datetime.now().timestamp()
	return f"{int(current_server_time - timestamp_ms)} seconds"

####################################################
###############_PARSE_FUNCTIONS_CODE_###############
####################################################

def parse_data_integer(data):
	return int(data, 16)

def int_multiply_01(data):
	return float(decimal.Decimal(int(data, 16)) * decimal.Decimal('0.1'))

def int_multiply_001(data):
	return float(decimal.Decimal(int(data, 16)) * decimal.Decimal('0.01')) 

def int_multiply_0001(data):
	return float(decimal.Decimal(int(data, 16)) * decimal.Decimal('0.001'))

def signed_no_multiply(data): #need more testing of this function
	try:
		binary = bytes.fromhex(data.zfill(8))
		value = struct.unpack(">i", binary)[0]
		return value
	except Exception as e:
		print(f"unexpected value received in function '{data}' error: '{e}' will leave unparsed value!")
		return f"0x{data}"

def parse_ibutton(data):
    """
    Special parser for iButton ID (parameter 78)
    Returns the iButton ID as a hex string without '0x' prefix
    """
    try:
        # Return the raw hex string without '0x' prefix
        return data.zfill(16)  # Ensure 16 characters (8 bytes)
    except Exception as e:
        print(f"Error parsing iButton ID: {e}")
        return f"0x{data}"

parse_functions_dictionary = { #this must simply be updated with new AVL IDs and their functions
	
	240: parse_data_integer,
	239: parse_data_integer,
	80: parse_data_integer,
	21: parse_data_integer,
	200: parse_data_integer,
	69: parse_data_integer,
	181: int_multiply_01,
	182: int_multiply_01,
	66: int_multiply_0001,
	24: parse_data_integer,
	205: parse_data_integer,
	206: parse_data_integer,
	67: int_multiply_0001,
	68: int_multiply_0001,
	241: parse_data_integer,
	299: parse_data_integer,
	16: parse_data_integer,
	1: parse_data_integer,
	9: parse_data_integer,
	179: parse_data_integer,
	12: int_multiply_0001,
	13: int_multiply_001,
	17: signed_no_multiply,
	18: signed_no_multiply,
	19: signed_no_multiply,
	11: parse_data_integer,
	10: parse_data_integer,
	2: parse_data_integer,
	3: parse_data_integer,
	6: int_multiply_0001,
	180: parse_data_integer,
	113: parse_data_integer,
	48: parse_data_integer,
	246: parse_data_integer,
	247: parse_data_integer,
	252: parse_data_integer,
	318: parse_data_integer,
	255: parse_data_integer,
	249: parse_data_integer,
	199: parse_data_integer,
	250: parse_data_integer,
	89: parse_data_integer,
	84: int_multiply_01,
	37: parse_data_integer,
	34: int_multiply_01,
	87: parse_data_integer,
	135: parse_data_integer,
	110: int_multiply_01,
	60: int_multiply_001,
	33: int_multiply_01,
	216: parse_data_integer,
	83: int_multiply_01,
    192: parse_data_integer,
    193: parse_data_integer,
    191: parse_data_integer,
    183: signed_no_multiply,
    1148: signed_no_multiply,
    78: parse_ibutton,
    86: parse_data_integer,
    201: parse_data_integer,

}

def sorting_hat(key, value):
	if key in parse_functions_dictionary:
		parse_function = parse_functions_dictionary[key]
		return parse_function(value)
	else:
		return f"0x{value}"
	
# Convert degrees to radians
def haversine_distance(lat1, lon1, lat2, lon2):
    R = 6371000  # Radius of Earth in meters
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat / 2)**2 + cos(lat1) * cos(lat2) * sin(dlon / 2)**2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    
    return R * c

# Get current timestamp in Italy Timezone
def get_italy_time():
    italy_tz = pytz.timezone("Europe/Rome")
    return datetime.datetime.now(italy_tz).strftime("%Y-%m-%d %H:%M:%S")

# Function to update route progress
def update_route_progress(imei, latitude, longitude, io_dict):
    date = datetime.datetime.now().strftime("%Y-%m-%d")
    file_path = f"{ROUTE_FOLDER}/{imei}/{date}.json"

    if not os.path.exists(file_path):
        print(f"No route file found for IMEI: {imei} on {date}")
        return

    try:
        # Load route data from JSON file
        with open(file_path, "r") as file:
            route_data = json.load(file)

        # If route is already completed, skip
        if route_data.get("status") == "completed":
            return

        # If route hasn't started yet and we're not at start point, skip
        if (route_data.get("status") == "pending" and 
            route_data["start_point"]["status"] == "pending"):
            distance_to_start = haversine_distance(
                float(latitude), float(longitude),
                float(route_data["start_point"]["latitude"]),
                float(route_data["start_point"]["longitude"])
            )
            if distance_to_start > RADIUS_THRESHOLD:
                return

        route_id = route_data["route_id"]
        payload = {
            "route_id": route_id,
            "io_data": io_dict,
            "timestamp": get_italy_time()
        }

        # Check if device is at Start Point
        start_point = route_data["start_point"]
        if start_point["status"] == "pending":
            distance = haversine_distance(
                float(latitude), float(longitude),
                float(start_point["latitude"]),
                float(start_point["longitude"])
            )
            if distance <= RADIUS_THRESHOLD:
                print("Vehicle reached start point, marking as started...")
                payload["type"] = "start_point"
                response = requests.post(UPDATE_ROUTE_API_URL, json=payload)
                if response.status_code == 200:
                    start_point["status"] = "completed"
                    route_data["status"] = "ongoing"

        # Only process stops if route has started
        if route_data["status"] == "ongoing":
            # Check stops in sequential order
            pending_stops = [stop for stop in route_data["stops"] if stop["status"] == "pending"]
            if pending_stops:
                current_stop = pending_stops[0]  # Get the first pending stop
                distance = haversine_distance(
                    float(latitude), float(longitude),
                    float(current_stop["latitude"]),
                    float(current_stop["longitude"])
                )
                if distance <= RADIUS_THRESHOLD:
                    print(f"Vehicle reached stop {current_stop['id']}, marking as completed...")
                    payload["type"] = "stop_point"
                    payload["stop_id"] = current_stop["id"]
                    response = requests.post(UPDATE_ROUTE_API_URL, json=payload)
                    if response.status_code == 200:
                        current_stop["status"] = "completed"

            # Check end point only if all stops are completed
            if all(stop["status"] == "completed" for stop in route_data["stops"]):
                end_point = route_data["end_point"]
                if end_point["status"] == "pending":
                    distance = haversine_distance(
                        float(latitude), float(longitude),
                        float(end_point["latitude"]),
                        float(end_point["longitude"])
                    )
                    if distance <= RADIUS_THRESHOLD:
                        print("Vehicle reached end point, marking as completed...")
                        payload["type"] = "end_point"
                        response = requests.post(UPDATE_ROUTE_API_URL, json=payload)
                        if response.status_code == 200:
                            end_point["status"] = "completed"

        # Check if route is fully completed
        all_stops_completed = all(stop["status"] == "completed" for stop in route_data["stops"])
        if (start_point["status"] == "completed" and 
            all_stops_completed and 
            route_data["end_point"]["status"] == "completed"):
            
            print("Route completed, calling complete-route API...")
            complete_payload = {
                "route_id": route_id,
                "timestamp": get_italy_time()
            }
            response = requests.post(COMPLETE_ROUTE_API, json=complete_payload)
            if response.status_code == 200:
                route_data["status"] = "completed"
                # Delete the file after completion
                os.remove(file_path)

        # Save updated JSON
        with open(file_path, "w") as file:
            json.dump(route_data, file, indent=4)

    except Exception as e:
        print(f"Error updating route progress: {e}")


####################################################

def fileAccessTest(): #check if script can create files and folders
	try:
		testDict = {}
		testDict["_Writing_Test_"] = "Writing_Test"
		testDict["Script_Started"] = time_stamper_for_json()

		# json_printer(testDict, "file_Write_Test")

		print (f"---### File access test passed! ###---")
		input_trigger()

	except Exception as e:
		print ()
		print (f"---### File access error occured ###---")
		print (f"'{e}'")
		print (f"---### Try running terminal with Administrator rights! ###---")
		print (f"---### Nothing will be saved if you decide to continue! ###---")
		print ()
		input_trigger()


def main():
	fileAccessTest()

if __name__ == "__main__":
	main()
