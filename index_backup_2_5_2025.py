import socket
import json
import requests
import os
import datetime
import struct
import decimal
import threading
from shapely.geometry import Point, Polygon
from geopy.distance import geodesic
from command import create_codec12_command, decode_command_response  # Import your command script
import time
import pytz
from math import radians, sin, cos, sqrt, atan2
from collections import defaultdict
from queue import PriorityQueue
import heapq

HOST = '0.0.0.0'  #function may not work in Linux systems, change to string with IP adress example: "***********"
PORT = 2020  #change this to your port

BASE_PATH = "/home/<USER>/ControlOne/public_html/public/"
COMMAND_QUEUE_PATH = BASE_PATH + "command/queue.json"
PLATFORM_BASE_URL = "https://piattaforma.controllone.it/"
LARAVEL_COMMAND_API_URL = PLATFORM_BASE_URL + "api/save-command-response"

# Constants for route progress
ROUTE_FOLDER = BASE_PATH + "routes"
UPDATE_ROUTE_API_URL = PLATFORM_BASE_URL + "api/update-route"
COMPLETE_ROUTE_API = PLATFORM_BASE_URL + "api/complete-route"
RADIUS_THRESHOLD = 500

# Constants for queue management
MAX_QUEUE_SIZE = 1000  # Prevent unbounded growth
MAX_PROCESSING_DELAY = 50  # Maximum seconds to wait for ordering
CLEANUP_INTERVAL = 600  # Cleanup old queues every 5 minutes

queue_lock = threading.Lock()
COMMAND_COOLDOWN = 5  # seconds

class DeviceMessageHandler:
    def __init__(self):
        self.device_queues = defaultdict(lambda: PriorityQueue(maxsize=MAX_QUEUE_SIZE))
        self.device_locks = defaultdict(threading.Lock)
        self.last_activity = defaultdict(float)
        # Add cache for processed timestamps to prevent duplicate processing
        self.processed_timestamps = defaultdict(set)
        # Cache for recent messages to avoid reprocessing
        self.message_cache = defaultdict(dict)
        # Maximum number of timestamps to keep in memory per device
        self.MAX_TIMESTAMP_CACHE = 1000
        # Add cache expiration time (24 hours in seconds)
        self.CACHE_EXPIRATION_TIME = 86400
        self._cleanup_thread = threading.Thread(target=self._cleanup_old_queues, daemon=True)
        self._cleanup_thread.start()

    def clear_all_queues(self):
        """Clear all device queues"""
        with threading.Lock():
            for imei in list(self.device_queues.keys()):
                while not self.device_queues[imei].empty():
                    try:
                        self.device_queues[imei].get_nowait()
                    except queue.Empty:
                        break
            self.last_activity.clear()
            self.processed_timestamps.clear()
            self.message_cache.clear()

    def _cleanup_timestamp_cache(self, device_imei):
        """
        Clean up the timestamp cache and message cache for a specific device:
        1. Remove expired entries based on CACHE_EXPIRATION_TIME
        2. Limit the cache size to MAX_TIMESTAMP_CACHE entries
        """
        current_time = time.time()

        # Skip if the device doesn't have any timestamps
        if device_imei not in self.processed_timestamps:
            return

        # Clean up timestamp cache
        # Convert to list for processing
        timestamps = list(self.processed_timestamps[device_imei])

        # Remove expired entries
        valid_timestamps = []
        for ts, creation_time in timestamps:
            if current_time - creation_time < self.CACHE_EXPIRATION_TIME:
                valid_timestamps.append((ts, creation_time))

        # Sort by timestamp (newest first)
        valid_timestamps.sort(key=lambda x: x[0], reverse=True)

        # Limit to MAX_TIMESTAMP_CACHE entries
        if len(valid_timestamps) > self.MAX_TIMESTAMP_CACHE:
            valid_timestamps = valid_timestamps[:self.MAX_TIMESTAMP_CACHE]

        # Update the set
        self.processed_timestamps[device_imei] = set(valid_timestamps)

        # Clean up message cache
        if device_imei in self.message_cache:
            # Remove expired entries from message cache
            message_keys = list(self.message_cache[device_imei].keys())
            for key in message_keys:
                cache_time = self.message_cache[device_imei][key]
                if current_time - cache_time >= self.CACHE_EXPIRATION_TIME:
                    del self.message_cache[device_imei][key]

            # Limit message cache size if needed
            if len(self.message_cache[device_imei]) > self.MAX_TIMESTAMP_CACHE * 2:  # Allow more location variations
                # Sort by timestamp (newest first)
                sorted_keys = sorted(self.message_cache[device_imei].items(),
                                    key=lambda x: x[1], reverse=True)
                # Keep only the newest entries
                self.message_cache[device_imei] = dict(sorted_keys[:self.MAX_TIMESTAMP_CACHE * 2])

    def _cleanup_old_queues(self):
        while True:
            current_time = time.time()
            with threading.Lock():
                for imei in list(self.last_activity.keys()):
                    if current_time - self.last_activity[imei] > CLEANUP_INTERVAL:
                        del self.device_queues[imei]
                        del self.device_locks[imei]
                        del self.last_activity[imei]
                        # Also clean up the timestamp cache and message cache
                        if imei in self.processed_timestamps:
                            del self.processed_timestamps[imei]
                        if imei in self.message_cache:
                            del self.message_cache[imei]
            time.sleep(CLEANUP_INTERVAL)

    def _get_record_number_from_data(self, hex_data):
        """
        Extract record number from the codec 8E packet.
        This is used to send proper acknowledgments for skipped messages.
        """
        try:
            # Parse packet header to get number of records
            data_field_length = int(hex_data[8:8+8], 16)
            number_of_records = int(hex_data[18:18+2], 16)
            return number_of_records
        except Exception as e:
            print(f"Error extracting record number: {e}")
            return 1  # Fallback to a safe value

    def _extract_location_from_data(self, hex_data):
        """
        Extract longitude and latitude from the codec 8E packet.
        This is used to create a more unique identifier for duplicate detection.
        Returns a tuple (longitude, latitude) or None if extraction fails.
        """
        try:
            # Skip to the AVL data section (after timestamp)
            avl_data_start = hex_data[20:]

            # In codec 8E, longitude is typically at position 18-26 in the AVL data
            # and latitude at position 26-34 (after timestamp and priority)
            longitude_hex = avl_data_start[18:26]
            latitude_hex = avl_data_start[26:34]

            # Return the raw hex values - we don't need to convert to actual coordinates
            # for duplicate detection purposes
            return (longitude_hex, latitude_hex)
        except Exception as e:
            print(f"Error extracting location data: {e}")
            return None

    def process_message(self, device_imei, data, conn, addr):
        try:
            hex_data = data.hex()
            timestamp = extract_timestamp_from_data(hex_data)
            current_time = time.time()

            # Skip very old messages (e.g., older than 24 hours)
            if (current_time - (timestamp / 1000)) > 86400:  # 86400 seconds = 24 hours
                print(f"Skipping old message from {device_imei}, age: {(current_time - (timestamp / 1000))/3600:.2f} hours")
                # Still send acknowledgment to prevent device from resending
                try:
                    record_number = self._get_record_number_from_data(hex_data)
                    record_response = (record_number).to_bytes(4, byteorder="big")
                    conn.sendall(record_response)
                except Exception as e:
                    print(f"Error sending old message ack to {device_imei}: {e}")
                    # Fallback to a generic acknowledgment
                    try:
                        conn.sendall((1).to_bytes(4, byteorder="big"))
                    except:
                        pass
                return True

            # Extract location data for more precise duplicate detection
            location_data = self._extract_location_from_data(hex_data)

            # Skip already processed messages (prevent duplicates)
            # Check if we have a message with the same timestamp AND location
            # is_duplicate = False

            # for stored_ts, creation_time in self.processed_timestamps[device_imei]:
            #     # If we find a matching timestamp, check if it's the same location
            #     if stored_ts == timestamp:
            #         # If no location data could be extracted, fall back to timestamp-only check
            #         if location_data is None:
            #             is_duplicate = True
            #             break

            #         # Check if we have this timestamp+location combination in the cache
            #         cache_key = f"{timestamp}_{location_data[0]}_{location_data[1]}"
            #         if cache_key in self.message_cache[device_imei]:
            #             is_duplicate = True
            #             break

            # if is_duplicate:
            #     print(f"Skipping duplicate message from {device_imei}, timestamp: {timestamp}")
            #     # Log additional debug information
            #     location_str = "unknown" if location_data is None else f"{location_data[0]}_{location_data[1]}"
            #     print(f"Duplicate details - IMEI: {device_imei}, Timestamp: {timestamp}, " +
            #           f"Location: {location_str}, First 20 chars: {hex_data[:20]}")

            #     # Still send acknowledgment to prevent device from resending
            #     try:
            #         # Parse enough to get the record number for proper acknowledgment
            #         record_number = self._get_record_number_from_data(hex_data)
            #         record_response = (record_number).to_bytes(4, byteorder="big")
            #         conn.sendall(record_response)
            #     except Exception as e:
            #         print(f"Error sending duplicate ack to {device_imei}: {e}")
            #         # Fallback to a generic acknowledgment
            #         try:
            #             conn.sendall((1).to_bytes(4, byteorder="big"))
            #         except:
            #             pass
            #     return True

            with self.device_locks[device_imei]:
                self.last_activity[device_imei] = current_time

                # Add to device queue with priority based on timestamp
                try:
                    self.device_queues[device_imei].put_nowait((-timestamp, data))
                except queue.Full:
                    # If queue is full, process oldest message first
                    self._process_oldest_message(device_imei, conn)
                    self.device_queues[device_imei].put((-timestamp, data))

                # Process messages that are ready
                self._process_ready_messages(device_imei, conn, current_time)

                # Add to processed timestamps cache with current time
                current_time = time.time()

                # Store timestamp with its creation time as a tuple (timestamp, creation_time)
                # First check if it's already in the set to avoid duplicates
                if timestamp not in {ts for ts, _ in self.processed_timestamps[device_imei]}:
                    self.processed_timestamps[device_imei].add((timestamp, current_time))

                # Store the location data in the message cache for more precise duplicate detection
                if location_data is not None:
                    cache_key = f"{timestamp}_{location_data[0]}_{location_data[1]}"
                    self.message_cache[device_imei][cache_key] = current_time

                # Clean up expired entries and limit the size of the timestamp cache
                self._cleanup_timestamp_cache(device_imei)

            return True
        except Exception as e:
            print(f"Error processing message for device {device_imei}: {e}")
            return False

    def _process_oldest_message(self, device_imei, conn):
        """Process the oldest message in the queue."""
        try:
            _, data = self.device_queues[device_imei].get_nowait()
            self._process_single_message(device_imei, data, conn)
        except queue.Empty:
            pass

    def _process_ready_messages(self, device_imei, conn, current_time):
        """Process all messages that are ready to be processed."""
        messages_processed = 0
        while not self.device_queues[device_imei].empty():
            try:
                neg_timestamp, data = self.device_queues[device_imei].queue[0]
                timestamp = -neg_timestamp

                # Check if message is ready to be processed
                time_diff = current_time - (timestamp / 1000)  # Convert to seconds
                if time_diff > MAX_PROCESSING_DELAY:
                    # Process old message
                    self.device_queues[device_imei].get_nowait()
                    self._process_single_message(device_imei, data, conn)
                    messages_processed += 1
                elif messages_processed > 0:
                    # If we've processed some messages but hit a new one, process it
                    self.device_queues[device_imei].get_nowait()
                    self._process_single_message(device_imei, data, conn)
                else:
                    # Wait for more messages
                    break
            except queue.Empty:
                break
            except Exception as e:
                print(f"Error in _process_ready_messages for {device_imei}: {e}")
                break

    def _process_single_message(self, device_imei, data, conn):
        """Process a single message with existing logic."""
        try:
            # Check if we have this message in cache
            hex_data = data.hex()
            record_number = codec_parser_trigger(hex_data, device_imei, "SERVER")

            # Send acknowledgment with proper record number
            try:
                record_response = (record_number).to_bytes(4, byteorder="big")
                conn.sendall(record_response)
            except Exception as e:
                print(f"Error sending acknowledgment to {device_imei}: {e}")
                # Try to reconnect or handle the error
                try:
                    # Fallback to a generic acknowledgment
                    conn.sendall((1).to_bytes(4, byteorder="big"))
                except:
                    print(f"Failed to send fallback acknowledgment to {device_imei}")
        except Exception as e:
            print(f"Error processing message for device {device_imei}: {e}")
            # Try to send a generic acknowledgment to prevent device from resending
            try:
                conn.sendall((1).to_bytes(4, byteorder="big"))
            except:
                pass

# Create global handler instance
message_handler = DeviceMessageHandler()

def handle_client(conn, addr):
    """
    Handles the connection with each client individually in a thread.
    Improved with better error handling and connection management.
    """
    print(f"Connected by {addr}")
    device_imei = "default_IMEI"
    conn.settimeout(5)

    # Track connection state
    connection_active = True
    last_activity_time = time.time()
    last_command_time = 0

    # Track packet processing to detect duplicates with timestamps
    # Store as (hash, timestamp) tuples to allow time-based expiration
    processed_packets = set()
    MAX_PROCESSED_PACKETS = 200  # Increased limit for better duplicate detection
    PACKET_CACHE_EXPIRATION = 3600  # Expire packets after 1 hour (in seconds)

    while connection_active:
        try:
            # Check for connection timeout (no activity for 60 seconds)
            current_time = time.time()
            if current_time - last_activity_time > 60:
                print(f"Connection inactive for too long with {addr} - closing")
                break

            # Receive data with proper error handling
            try:
                data = conn.recv(10240)  # receive data from the client
                last_activity_time = time.time()  # Update activity time
            except socket.timeout:
                # Just continue on timeout - allows checking for inactivity
                continue

            # Check if connection was closed
            if not data:
                print(f"Connection closed by client {addr}")
                break

            # Calculate a more robust hash of the data for duplicate detection
            # Use a combination of IMEI, timestamp, and location data for better uniqueness
            hex_data = data.hex()

            # Try to extract timestamp and location data for a more precise hash
            try:
                # Extract timestamp (typically at position 20-36)
                timestamp_hex = hex_data[20:36] if len(hex_data) >= 36 else ""

                # Extract location data (typically after timestamp and priority)
                # In codec 8E, longitude is at position 38-46 and latitude at 46-54 in the full packet
                longitude_hex = hex_data[38:46] if len(hex_data) >= 46 else ""
                latitude_hex = hex_data[46:54] if len(hex_data) >= 54 else ""

                # Create a hash that includes IMEI, timestamp, and location
                data_hash = hash(f"{device_imei}_{timestamp_hex}_{longitude_hex}_{latitude_hex}")
            except Exception:
                # Fallback to simpler hash if extraction fails
                data_hash = hash(f"{device_imei}_{hex_data[:200]}")

            # Process IMEI identification
            if imei_checker(data.hex()):
                device_imei = ascii_imei_converter(data.hex())
                try:
                    conn.sendall((1).to_bytes(1, byteorder="big"))
                    print(f"IMEI received {device_imei}")
                except Exception as e:
                    print(f"Error sending IMEI acknowledgment to {device_imei}: {e}")
                    break  # Break if we can't send acknowledgment

                # Clear processed packets when device identifies itself
                processed_packets.clear()

            # Process Codec 8E data
            elif codec_8e_checker(data.hex().replace(" ", "")):
                # Check for duplicate packets (same device, same data)
                # Extract just the hashes from the processed_packets set for comparison
                packet_hashes = {h for h, _ in processed_packets}
                if data_hash in packet_hashes:
                    print(f"Duplicate packet detected from {device_imei} - sending ack only")
                    # Log additional debug information
                    print(f"Duplicate details - IMEI: {device_imei}, " +
                          f"Data hash: {data_hash}, First 20 chars: {hex_data[:20]}")

                    try:
                        # Still need to send acknowledgment to prevent device from resending
                        record_number = message_handler._get_record_number_from_data(hex_data)
                        record_response = (record_number).to_bytes(4, byteorder="big")
                        conn.sendall(record_response)

                        # Update last activity time even for duplicates
                        last_activity_time = time.time()

                        continue  # Skip further processing
                    except Exception as e:
                        print(f"Error sending duplicate ack: {e}")
                        # Continue with normal processing as fallback

                # Add to processed packets set with current timestamp
                current_time = time.time()
                processed_packets.add((data_hash, current_time))

                # Clean up expired entries and limit the size
                # First, remove expired entries
                valid_packets = set()
                for packet_hash, timestamp in processed_packets:
                    if current_time - timestamp < PACKET_CACHE_EXPIRATION:
                        valid_packets.add((packet_hash, timestamp))

                # Then limit the size if still too large
                if len(valid_packets) > MAX_PROCESSED_PACKETS:
                    # Convert to list, sort by timestamp (newest first), and keep newest entries
                    packets_list = sorted(list(valid_packets), key=lambda x: x[1], reverse=True)
                    valid_packets = set(packets_list[:MAX_PROCESSED_PACKETS])

                # Update the set
                processed_packets = valid_packets

                # Handle data with message handler
                if not message_handler.process_message(device_imei, data, conn, addr):
                    print(f"Error processing message from {device_imei} - continuing")

                # Command handling with improved error handling
                try:
                    current_time = time.time()
                    if current_time - last_command_time < COMMAND_COOLDOWN:
                        time.sleep(COMMAND_COOLDOWN - (current_time - last_command_time))

                    # Fetch the next command for the IMEI
                    command_data, queue_data = fetch_next_command(device_imei)
                    if not command_data:
                        continue

                    command = command_data.get("command")
                    if not command:
                        print(f"Command data empty for {device_imei}")
                        continue

                    encoded_command = create_codec12_command(command)
                    print(f"Sending command to device: {command}")
                    conn.sendall(encoded_command)  # Send encoded command

                    # Save updated queue data (remove the sent command)
                    save_queue(queue_data)

                    # Wait for and decode the response with timeout
                    conn.settimeout(10)  # Temporary longer timeout for command response
                    try:
                        response_data = conn.recv(10240)
                        print(f"Response received: {response_data.hex()}")
                        decoded_response = decode_command_response(response_data.hex())
                        print(f"Decoded Response: {decoded_response.response}")

                        if decoded_response:
                            # Send the payload to Laravel API
                            send_to_laravel_api(device_imei, command, decoded_response.response)
                    except socket.timeout:
                        print(f"Timeout waiting for command response from {device_imei}")
                    finally:
                        conn.settimeout(5)  # Reset timeout to normal

                    last_command_time = time.time()
                except Exception as e:
                    print(f"Error in command handling for {device_imei}: {e}")

            else:
                print(f"Invalid data received from {addr} - dropping connection")
                break

        except Exception as e:
            print(f"Unexpected error handling client {addr}: {e}")
            # Only break on critical errors
            if isinstance(e, (ConnectionError, OSError)):
                break

    # Clean up
    try:
        conn.close()
    except:
        pass
    print(f"Connection closed with {addr}")


def input_trigger():
	print("Paste full 'Codec 8' packet to parse it or:")
	print("Type SERVER to start the server or:")
	print("Type EXIT to stop the program")
	device_imei = "default_IMEI"
	user_input = input("waiting for input: ")
	if user_input.upper() == "EXIT":
		print(f"exiting program............")
		exit()

	elif user_input.upper() == "SERVER":
		start_server_trigger()
	else:
		try:
			if codec_8e_checker(user_input.replace(" ","")) == False:
				print("Wrong input or invalid Codec8 packet")
				print()
				input_trigger()
			else:
				codec_parser_trigger(user_input, device_imei, "USER")
		except Exception as e:
			print(f"error occured: {e} enter proper Codec8 packet or EXIT!!!")
			input_trigger()

def start_server_trigger():
    print("Starting server!")
    # Clear all queues on startup
    message_handler.clear_all_queues()

    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        # ✅ Allow the socket to be reused immediately after closing
        s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

        s.bind((HOST, PORT))
        s.listen()  # listen for incoming connections
        print(f"Server listening on {HOST}:{PORT}")

        while True:
            conn, addr = s.accept()  # accept a new client connection
            # Start a new thread to handle each client
            client_thread = threading.Thread(target=handle_client, args=(conn, addr))
            client_thread.start()  # start the thread for handling the client

####################################################
###############__CRC16/ARC Checker__################
####################################################

def crc16_arc(data):
	data_part_length_crc = int(data[8:16], 16)
	data_part_for_crc = bytes.fromhex(data[16:16+2*data_part_length_crc])
	crc16_arc_from_record = data[16+len(data_part_for_crc.hex()):24+len(data_part_for_crc.hex())]

	crc = 0

	for byte in data_part_for_crc:
		crc ^= byte
		for _ in range(8):
			if crc & 1:
				crc = (crc >> 1) ^ 0xA001
			else:
				crc >>= 1

	if crc16_arc_from_record.upper() == crc.to_bytes(4, byteorder='big').hex().upper():
		print ("CRC check passed!")
		print (f"Record length: {len(data)} characters // {int(len(data)/2)} bytes")
		return True
	else:
		print("CRC check Failed!")
		return False

####################################################

def codec_8e_checker(codec8_packet):
    """
    Enhanced codec checker with better validation and error handling.
    Validates packet structure before checking codec type and CRC.
    """
    try:
        # Basic validation - check if input is a string and has minimum length
        if not isinstance(codec8_packet, str):
            print(f"Invalid packet type: {type(codec8_packet)}")
            return False

        # Check for minimum length (preamble + length + codec type)
        if len(codec8_packet) < 18:
            print(f"Packet too short ({len(codec8_packet)} chars): {codec8_packet[:18]}")
            return False

        # Validate that the packet contains only hex characters
        if not all(c in '0123456789ABCDEFabcdef' for c in codec8_packet):
            print(f"Packet contains non-hex characters")
            return False

        # Check for valid preamble (first 8 chars should be zeros)
        if not codec8_packet.startswith('00000000'):
            print(f"Invalid preamble: {codec8_packet[:8]}")
            return False

        # Check data field length for consistency
        try:
            data_field_length = int(codec8_packet[8:16], 16)
            expected_packet_length = 8 + 8 + (2 * data_field_length) + 8  # preamble + length + data + crc

            # Allow some flexibility in packet length (±10 chars)
            if abs(len(codec8_packet) - expected_packet_length) > 10:
                print(f"Packet length mismatch: expected ~{expected_packet_length}, got {len(codec8_packet)}")
                # Don't return False here - some devices send extra data
        except ValueError:
            print(f"Invalid data field length: {codec8_packet[8:16]}")
            return False

        # Check codec type
        codec_type = str(codec8_packet[16:18]).upper()
        if codec_type != "8E" and codec_type != "08":
            print(f"Invalid codec type: {codec_type}")
            return False

        # If all basic checks pass, perform CRC check
        return crc16_arc(codec8_packet)

    except Exception as e:
        print(f"Error checking codec: {e}")
        # Print packet info for debugging (first 20 chars only to avoid log spam)
        if isinstance(codec8_packet, str) and len(codec8_packet) > 0:
            print(f"Problematic packet (first 20 chars): {codec8_packet[:20]}...")
        return False

def codec_parser_trigger(codec8_packet, device_imei, props):
    """
    Trigger the codec parser with improved error handling and caching.
    """
    try:
        # Create a clean packet by removing spaces
        clean_packet = codec8_packet.replace(" ","")

        # Check if we're in server mode (for caching)
        if props == "SERVER":
            # Use a static cache to avoid reprocessing identical packets
            if not hasattr(codec_parser_trigger, "packet_cache"):
                codec_parser_trigger.packet_cache = {}

            # Create a cache key based on device_imei and packet
            # Only use the first 100 chars of packet to avoid memory issues with large packets
            cache_key = f"{device_imei}_{clean_packet[:100]}"

            # Check if we've processed this packet before
            if cache_key in codec_parser_trigger.packet_cache:
                print(f"Using cached result for packet from {device_imei}")
                return codec_parser_trigger.packet_cache[cache_key]

            # Process the packet
            result = codec_8e_parser(clean_packet, device_imei, props)

            # Cache the result (limit cache size to prevent memory leaks)
            if len(codec_parser_trigger.packet_cache) > 1000:
                # Clear half the cache when it gets too large
                keys_to_remove = list(codec_parser_trigger.packet_cache.keys())[:500]
                for key in keys_to_remove:
                    del codec_parser_trigger.packet_cache[key]

            codec_parser_trigger.packet_cache[cache_key] = result
            return result
        else:
            # For user mode, just process without caching
            return codec_8e_parser(clean_packet, device_imei, props)

    except Exception as e:
        print(f"Error occurred in codec_parser_trigger: {e}")
        if props == "USER":
            input_trigger()
        else:
            # For server mode, return a safe value
            return 1  # Return 1 record as a safe default

def imei_checker(hex_imei):
    try:
        ascii_imei = bytes.fromhex(hex_imei[4:]).decode()
        return len(ascii_imei) == 15
    except:
        return False

def ascii_imei_converter(hex_imei):
	return bytes.fromhex(hex_imei[4:]).decode()

def extract_timestamp_from_data(hex_data):
    """
    Extract timestamp from the codec 8E packet.
    Returns timestamp as integer for ordering.
    Enhanced with better error handling and validation.
    """
    try:
        # Validate input
        if not hex_data or len(hex_data) < 36:
            print(f"Invalid hex data for timestamp extraction: {hex_data[:20]}...")
            return int(time.time() * 1000)  # Fallback to current time

        # Check codec type to determine correct position
        codec_type = None
        if len(hex_data) >= 18:
            codec_type = hex_data[16:18].upper()

        # Adjust position based on codec type
        if codec_type == "8E" or codec_type == "08":
            timestamp_hex = hex_data[20:36]  # Standard position for these codecs
        else:
            # Try to find timestamp based on packet structure
            # This is a fallback mechanism
            if len(hex_data) >= 36:
                timestamp_hex = hex_data[20:36]
            else:
                print(f"Packet too short for timestamp extraction")
                return int(time.time() * 1000)

        # Validate timestamp format
        if not all(c in '0123456789ABCDEFabcdef' for c in timestamp_hex):
            print(f"Invalid timestamp format: {timestamp_hex}")
            return int(time.time() * 1000)

        # Convert to integer
        timestamp = int(timestamp_hex, 16)

        # Sanity check - timestamp should be within reasonable range
        current_time_ms = int(time.time() * 1000)
        ten_years_ms = 10 * 365 * 24 * 60 * 60 * 1000

        if timestamp > current_time_ms + 86400000 or timestamp < current_time_ms - ten_years_ms:
            print(f"Timestamp out of reasonable range: {timestamp}")
            return current_time_ms

        return timestamp

    except Exception as e:
        print(f"Error extracting timestamp: {e}")
        return int(time.time() * 1000)  # Fallback to current time


# gprs command queue & response handling
def fetch_next_command(imei):
    """
    Fetches the next command for the given IMEI from the queue.json file.
    """
    with queue_lock:  # Ensure thread-safe access
        try:
            with open(COMMAND_QUEUE_PATH, "r") as file:
                command_data = json.load(file)

            if imei in command_data and command_data[imei]:
                # Pop the first command from the queue for the given IMEI
                command_to_send = command_data[imei].pop(0)
                return command_to_send, command_data
            return None, command_data
        except FileNotFoundError:
            print("Command queue file not found.")
            return None, {}
        except json.JSONDecodeError:
            print("Error decoding JSON file.")
            return None, {}

def save_queue(command_data):
    """
    Saves the updated queue back to the JSON file.
    """
    with queue_lock:  # Ensure thread-safe access
        with open(COMMAND_QUEUE_PATH, "w") as file:
            json.dump(command_data, file, indent=4)

def send_to_laravel_api(imei, command, response):
    """
    Sends the decoded response to the Laravel API.
    """
    payload = {
        "imei": imei,
        "command": command,
        "response": response.decode('utf-8', errors='ignore')
    }
    try:
        response = requests.post(LARAVEL_COMMAND_API_URL, json=payload)
        if response.status_code == 200:
            print("Data successfully sent to Laravel API.")
        else:
            print(f"Failed to send data to Laravel API: {response.text}")
    except Exception as e:
        print(f"Error sending data to Laravel API: {e}")

####################################################
###############_Codec8E_parser_code_################
####################################################

def codec_8e_parser(codec_8E_packet, device_imei, props): #think a lot before modifying  this function
	# print()
#	print (str("codec 8 string entered - " + codec_8E_packet))

# 	io_dict_raw = {}
# 	timestamp = codec_8E_packet[20:36]
# 	io_dict_raw["device_IMEI"] = device_imei
# 	io_dict_raw["last_update"] = device_time_stamper(timestamp)
# #	io_dict_raw["_timestamp_"] = device_time_stamper(timestamp)
# #	io_dict_raw["_rec_delay_"] = record_delay_counter(timestamp)
# 	io_dict_raw["data_length"] = "Record length: " + str(int(len(codec_8E_packet))) + " characters" + " // " + str(int(len(codec_8E_packet) // 2)) + " bytes"
# 	io_dict_raw["_raw_data__"] = codec_8E_packet

	# try: #writing raw DATA dictionary to ./data/data.json
	# 	json_printer_rawDATA(io_dict_raw, device_imei)
	# except Exception as e:
	# 	print(f"JSON raw data writing error occured = {e}")

	zero_bytes = codec_8E_packet[:8]
	# print()
	# print (str("zero bytes = " + zero_bytes))

	data_field_length = int(codec_8E_packet[8:8+8], 16)
	# print (f"data field lenght = {data_field_length} bytes")
	codec_type = str(codec_8E_packet[16:16+2])
	# print (f"codec type = {codec_type}")

	data_step = 4
	if codec_type == "08":
		data_step = 2
	else:
		pass

	number_of_records = int(codec_8E_packet[18:18+2], 16)
	# print (f"number of records = {number_of_records}")

	record_number = 1
	avl_data_start = codec_8E_packet[20:]
	data_field_position = 0
	while data_field_position < (2*data_field_length-6):
		io_dict = {}
		io_dict["device_IMEI"] = device_imei
		# print()
		# print (f"data from record {record_number}")
		# print (f"########################################")

		timestamp = avl_data_start[data_field_position:data_field_position+16]
		io_dict["last_update"] = time_stamper_for_json()
		io_dict["_timestamp_"] = device_time_stamper(timestamp)
		# print (f"timestamp = {device_time_stamper(timestamp)}")
		io_dict["_rec_delay_"] = record_delay_counter(timestamp)
		data_field_position += len(timestamp)

		priority = avl_data_start[data_field_position:data_field_position+2]
		io_dict["priority"] = int(priority, 16)
		# print (f"record priority = {int(priority, 16)}")
		data_field_position += len(priority)

		longitude = avl_data_start[data_field_position:data_field_position+8]
	#	io_dict["longitude"] = struct.unpack('>i', bytes.fromhex(longitude))[0]
	#	print (f"longitude = {struct.unpack('>i', bytes.fromhex(longitude))[0]}")
		io_dict["longitude"] = coordinate_formater(longitude)
		# print (f"longitude = {coordinate_formater(longitude)}")
		data_field_position += len(longitude)

		latitude = avl_data_start[data_field_position:data_field_position+8]
	#	print (f"latitude = {struct.unpack('>i', bytes.fromhex(latitude))[0]}")
	#	io_dict["latitude"] = struct.unpack('>i', bytes.fromhex(latitude))[0]
		io_dict["latitude"] = coordinate_formater(latitude)
		# print (f"latitude = {coordinate_formater(latitude)}")
		data_field_position += len(latitude)

		altitude = avl_data_start[data_field_position:data_field_position+4]
		# print(f"altitude = {int(altitude, 16)}")
		io_dict["altitude"] = int(altitude, 16)
		data_field_position += len(altitude)

		angle = avl_data_start[data_field_position:data_field_position+4]
		# print(f"angle = {int(angle, 16)}")
		io_dict["angle"] = int(angle, 16)
		data_field_position += len(angle)

		satelites = avl_data_start[data_field_position:data_field_position+2]
		# print(f"satelites = {int(satelites, 16)}")
		io_dict["satelites"] = int(satelites, 16)
		data_field_position += len(satelites)

		speed = avl_data_start[data_field_position:data_field_position+4]
		io_dict["speed"] = int(speed, 16)
		# print(f"speed = {int(speed, 16)}")
		data_field_position += len(speed)

		# Parse the event ID
		event_io_id = avl_data_start[data_field_position:data_field_position+data_step]
		event_id = int(event_io_id, 16)
		io_dict["eventID"] = int(event_io_id, 16)
		# print(f"event ID = {int(event_io_id, 16)}")
		data_field_position += len(event_io_id)

		total_io_elements = avl_data_start[data_field_position:data_field_position+data_step]
		total_io_elements_parsed = int(total_io_elements, 16)
		# print(f"total I/O elements in record {record_number} = {total_io_elements_parsed}")
		data_field_position += len(total_io_elements)

		byte1_io_number = avl_data_start[data_field_position:data_field_position+data_step]
		byte1_io_number_parsed = int(byte1_io_number, 16)
		# print(f"1 byte io count = {byte1_io_number_parsed}")
		data_field_position += len(byte1_io_number)

		if byte1_io_number_parsed > 0:
			i = 1
			while i <= byte1_io_number_parsed:
				key = avl_data_start[data_field_position:data_field_position+data_step]
				data_field_position += len(key)
				value = avl_data_start[data_field_position:data_field_position+2]

				io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
				data_field_position += len(value)
				# print (f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				i += 1
		else:
			pass

		byte2_io_number = avl_data_start[data_field_position:data_field_position+data_step]
		byte2_io_number_parsed = int(byte2_io_number, 16)
		# print(f"2 byte io count = {byte2_io_number_parsed}")
		data_field_position += len(byte2_io_number)

		if byte2_io_number_parsed > 0:
			i = 1
			while i <= byte2_io_number_parsed:
				key = avl_data_start[data_field_position:data_field_position+data_step]
				data_field_position += len(key)

				value = avl_data_start[data_field_position:data_field_position+4]
				io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
				data_field_position += len(value)
				# print (f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				i += 1
		else:
			pass

		byte4_io_number = avl_data_start[data_field_position:data_field_position+data_step]
		byte4_io_number_parsed = int(byte4_io_number, 16)
		# print(f"4 byte io count = {byte4_io_number_parsed}")
		data_field_position += len(byte4_io_number)

		if byte4_io_number_parsed > 0:
			i = 1
			while i <= byte4_io_number_parsed:
				key = avl_data_start[data_field_position:data_field_position+data_step]
				data_field_position += len(key)

				value = avl_data_start[data_field_position:data_field_position+8]
				io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
				data_field_position += len(value)
				# print(f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				i += 1
		else:
			pass

		byte8_io_number = avl_data_start[data_field_position:data_field_position+data_step]
		byte8_io_number_parsed = int(byte8_io_number, 16)
		# print(f"8 byte io count = {byte8_io_number_parsed}")
		data_field_position += len(byte8_io_number)

		if byte8_io_number_parsed > 0:
			i = 1
			while i <= byte8_io_number_parsed:
				key = avl_data_start[data_field_position:data_field_position+data_step]
				data_field_position += len(key)

				value = avl_data_start[data_field_position:data_field_position+16]
				io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
				data_field_position += len(value)
				# print(f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				i += 1
		else:
			pass

		if codec_type.upper() == "8E":

			byteX_io_number = avl_data_start[data_field_position:data_field_position+4]
			byteX_io_number_parsed = int(byteX_io_number, 16)
			# print(f"X byte io count = {byteX_io_number_parsed}")
			data_field_position += len(byteX_io_number)

			if byteX_io_number_parsed > 0:
				i = 1
				while i <= byteX_io_number_parsed:
					key = avl_data_start[data_field_position:data_field_position+4]
					data_field_position += len(key)

					value_length = avl_data_start[data_field_position:data_field_position+4]
					data_field_position += 4
					value = avl_data_start[data_field_position:data_field_position+(2*(int(value_length, 16)))]
					io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
					data_field_position += len(value)
					# print(f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				#	print (f"data field postition = {data_field_position}")
				#	print (f"data_field_length = {2*data_field_length}")
					i += 1
			else:
				pass
		else:
			pass

		record_number += 1

		try: #writing dictionary to ./data/data.json
			json_printer(io_dict, device_imei)
		except Exception as e:
			print(f"JSON writing error occured = {e}")

	    # Format coordinates
		latitude = coordinate_formater(latitude)
		longitude = coordinate_formater(longitude)
		current_point = Point(float(longitude), float(latitude))

		# Construct the geofence file path
		geofence_file = f"{BASE_PATH}geofences/{device_imei}.json"

		# Check if the geofence file exists
		if os.path.exists(geofence_file):
			try:
				# Load geofence data
				with open(geofence_file, "r") as file:
					geofences = json.load(file)

				# print(f"Checking geofences {geofences}")

				# Process each geofence
				for geofence in geofences:
					geofence_data = geofence["geofence"]
					inside_geofence = is_point_in_geofence(current_point, geofence_data)

					new_state = "in" if inside_geofence else "out"
					prev_state, new_state = update_geofence_state(device_imei, new_state)

					# print(f"Checking states {new_state}, {prev_state}")

					if prev_state == "in" and new_state == "out":
						# Log geofence exit event
						log_geofence_event_to_backend(device_imei, {
							"longitude": longitude,
							"latitude": latitude,
							"geofence_id": geofence["id"],
							"type": "geofence_exit"
						})

					elif prev_state == "out" and new_state == "in":
						# Log geofence entry event
						log_geofence_event_to_backend(device_imei, {
							"longitude": longitude,
							"latitude": latitude,
							"geofence_id": geofence["id"],
							"type": "geofence_entry"
						})

			except json.JSONDecodeError:
				print(f"Invalid JSON in geofence file for device {device_imei}. Skipping geofence processing.")
			except Exception as e:
				print(f"Unexpected error while processing geofence for device {device_imei}: {e}")



		# update_route_progress(device_imei, latitude, longitude, io_dict)

		if event_id != 0 and (event_id == 249 or event_id == 247 or event_id == 246 or event_id == 252 or event_id == 318):
			send_event_to_api(device_imei, io_dict)



	if props == "SERVER":

		total_records_parsed = int(avl_data_start[data_field_position:data_field_position+2], 16)
		# print()
		# print(f"total parsed records = {total_records_parsed}")
		# print()
		return int(number_of_records)

	else:
		total_records_parsed = int(avl_data_start[data_field_position:data_field_position+2], 16)
		# print()
		# print(f"total parsed records = {total_records_parsed}")
		# print()


		input_trigger()

####################################################
###############_End_of_MAIN_Parser_Code#############
####################################################

####################################################
###############_Coordinates_Function_###############
####################################################

def coordinate_formater(hex_coordinate): #may return too large longitude need to test this more
	coordinate = int(hex_coordinate, 16)
	dec_coordinate = coordinate / 10000000
	if coordinate & (1 << 31):
		dec_coordinate *= -1
	else:
		pass
	return dec_coordinate



####################################################
###############____JSON_Functions____###############
####################################################

def parse_custom_datetime(date_str):
    try:
        return datetime.datetime.strptime(date_str.split(" (")[0], "%H:%M:%S %d-%m-%Y")
    except ValueError:
        return None


# printing device data to JSON file
def json_printer(io_dict, device_imei):
    # Update live data
    update_live_data(io_dict, device_imei)

    # Store historical data
    store_historical_data(io_dict, device_imei)

# Create a global lock for live_data file access
live_data_lock = threading.Lock()

def sanitize_dict(input_dict):
    """
    Deep sanitize the input dictionary to handle malformed JSON data.
    """
    if not isinstance(input_dict, dict):
        return {}

    sanitized = {}
    for key, value in input_dict.items():
        try:
            # Convert key to string if it's not already
            str_key = str(key)

            # Handle different value types
            if isinstance(value, dict):
                sanitized[str_key] = sanitize_dict(value)
            elif isinstance(value, (list, tuple)):
                sanitized[str_key] = [sanitize_dict(item) if isinstance(item, dict) else item
                                    for item in value if item is not None]
            elif value is not None:
                # Convert value to string and back to ensure JSON compatibility
                json.dumps(value)  # This will raise an error if value is not JSON serializable
                sanitized[str_key] = value
        except (TypeError, ValueError, json.JSONDecodeError):
            # If value is not JSON serializable, convert to string
            try:
                sanitized[str_key] = str(value)
            except:
                continue

    return sanitized

def repair_json_file(file_path, expect_list=False):
    """
    Attempt to repair corrupted JSON file by removing incomplete entries.
    :param file_path: Path to the JSON file
    :param expect_list: If True, ensures return value is a list
    :return: Repaired data structure (list or dict depending on expect_list)
    """
    try:
        with open(file_path, 'r') as file:
            content = file.read().strip()
            if not content:
                return [] if expect_list else {}

            # Handle list-type JSON files
            if content.startswith('['):
                depth = 0
                last_complete = 0
                in_string = False
                escape = False

                for i, char in enumerate(content):
                    if not in_string:
                        if char == '[':
                            depth += 1
                        elif char == ']':
                            depth -= 1
                            if depth == 0:
                                last_complete = i + 1
                        elif char == '"':
                            in_string = True
                    else:
                        if char == '\\':
                            escape = not escape
                        elif char == '"' and not escape:
                            in_string = False
                        else:
                            escape = False

                # Extract the valid portion of the JSON
                valid_content = content[:last_complete]
                if not valid_content or valid_content == '[':
                    return []

                try:
                    return json.loads(valid_content)
                except json.JSONDecodeError:
                    return []

            # Handle dict-type JSON files
            else:
                depth = 0
                last_complete = 0
                in_string = False
                escape = False

                for i, char in enumerate(content):
                    if not in_string:
                        if char == '{':
                            depth += 1
                        elif char == '}':
                            depth -= 1
                            if depth == 0:
                                last_complete = i + 1
                        elif char == '"':
                            in_string = True
                    else:
                        if char == '\\':
                            escape = not escape
                        elif char == '"' and not escape:
                            in_string = False
                        else:
                            escape = False

                # Extract the valid portion of the JSON
                valid_content = content[:last_complete]
                if not valid_content or valid_content == '{':
                    return [] if expect_list else {}

                try:
                    result = json.loads(valid_content)
                    return [] if expect_list else result
                except json.JSONDecodeError:
                    return [] if expect_list else {}

    except Exception as e:
        print(f"Error repairing JSON file: {e}")
        return [] if expect_list else {}

def update_live_data(io_dict, device_imei):
    """
    Update live data for a device with optimized I/O handling.
    Uses in-memory caching to reduce disk writes.
    """
    # Static cache for live data to reduce disk reads
    if not hasattr(update_live_data, "live_data_cache"):
        update_live_data.live_data_cache = {}
        update_live_data.last_write_time = 0
        update_live_data.write_interval = 1.0  # Seconds between writes to reduce I/O

    live_data_path = BASE_PATH + "data"
    live_data_file = "live.json"
    file_path = os.path.join(live_data_path, live_data_file)
    temp_file_path = file_path + ".tmp"
    backup_file_path = file_path + ".bak"

    # Ensure the directory exists
    os.makedirs(live_data_path, exist_ok=True)

    with live_data_lock:
        current_time = time.time()

        # Read from cache or file if cache is empty
        if not update_live_data.live_data_cache:
            # Read existing data with recovery mechanism
            if os.path.exists(file_path):
                try:
                    with open(file_path, "r") as file:
                        update_live_data.live_data_cache = json.load(file)
                except json.JSONDecodeError:
                    print("JSON decode error, attempting repair...")
                    # Create backup of corrupted file
                    if os.path.exists(file_path):
                        try:
                            os.replace(file_path, backup_file_path)
                        except OSError:
                            pass

                    # Attempt to repair the JSON
                    update_live_data.live_data_cache = repair_json_file(backup_file_path)
            else:
                update_live_data.live_data_cache = {}

        # Sanitize input dictionary
        io_dict = sanitize_dict(io_dict)

        # Skip update if no new data is provided
        if not io_dict:
            print(f"No valid data to update for device {device_imei}. Skipping update.")
            return

        # Ensure the device IMEI exists in live_data
        if device_imei not in update_live_data.live_data_cache:
            update_live_data.live_data_cache[device_imei] = {}

        # Update data from io_dict
        update_live_data.live_data_cache[device_imei].update(io_dict)

        # Only write to disk if enough time has passed since last write
        # This reduces I/O load significantly for high-frequency updates
        if current_time - update_live_data.last_write_time >= update_live_data.write_interval:
            # Write data atomically with multiple safeguards
            try:
                # First write to temporary file
                with open(temp_file_path, "w") as temp_file:
                    # Verify data is JSON serializable before writing
                    json_str = json.dumps(update_live_data.live_data_cache, ensure_ascii=False, separators=(',', ':'))
                    temp_file.write(json_str)
                    temp_file.flush()
                    os.fsync(temp_file.fileno())

                # Verify the temporary file is valid JSON
                with open(temp_file_path, "r") as verify_file:
                    json.load(verify_file)

                # If verification passes, perform atomic replace
                os.replace(temp_file_path, file_path)
                update_live_data.last_write_time = current_time

            except (TypeError, IOError, json.JSONDecodeError) as e:
                print(f"Error writing JSON data: {e}")
                # Clean up temporary file if it exists
                if os.path.exists(temp_file_path):
                    try:
                        os.remove(temp_file_path)
                    except OSError:
                        pass

def store_historical_data(io_dict, device_imei):
    """
    Store historical data for a device with optimized I/O handling.
    Uses caching to reduce disk reads/writes and deduplication to prevent duplicates.
    """
    # Static cache for historical data to reduce disk reads/writes
    if not hasattr(store_historical_data, "daily_data_cache"):
        store_historical_data.daily_data_cache = {}
        store_historical_data.dates_cache = {}
        store_historical_data.last_write_time = {}
        store_historical_data.write_interval = 2.0  # Seconds between writes

    history_path = f"{BASE_PATH}data/history/{device_imei}"
    date_file = "dates.json"

    italy_timezone = pytz.timezone('Europe/Rome')
    current_date = datetime.datetime.now(italy_timezone).strftime('%d-%m-%Y')
    data_file = f"{current_date}.json"

    if not os.path.exists(history_path):
        os.makedirs(history_path)

    # Create a cache key for this device and date
    cache_key = f"{device_imei}_{current_date}"
    current_time = time.time()

    # Initialize cache entry if needed
    if cache_key not in store_historical_data.last_write_time:
        store_historical_data.last_write_time[cache_key] = 0

    # Update dates.json with error recovery (using cache)
    date_path = os.path.join(history_path, date_file)

    # Load dates from cache or file
    if device_imei not in store_historical_data.dates_cache:
        dates = set()
        if os.path.exists(date_path):
            try:
                with open(date_path, "r") as file:
                    dates = set(json.load(file))
            except json.JSONDecodeError:
                print("Dates file corrupted, attempting repair...")
                dates = set()
        store_historical_data.dates_cache[device_imei] = dates

    # Update dates cache
    store_historical_data.dates_cache[device_imei].add(current_date)

    # Only write dates file if enough time has passed
    if current_time - store_historical_data.last_write_time.get(f"{device_imei}_dates", 0) >= store_historical_data.write_interval:
        sorted_dates = sorted(store_historical_data.dates_cache[device_imei],
                             key=lambda d: datetime.datetime.strptime(d, '%d-%m-%Y'),
                             reverse=True)

        # Write dates atomically
        temp_date_path = date_path + ".tmp"
        try:
            with open(temp_date_path, "w") as file:
                json.dump(sorted_dates, file, separators=(',', ':'))
            os.replace(temp_date_path, date_path)
            store_historical_data.last_write_time[f"{device_imei}_dates"] = current_time
        except Exception as e:
            print(f"Error writing dates file: {e}")
            if os.path.exists(temp_date_path):
                os.remove(temp_date_path)

    # Update daily data file with error recovery (using cache)
    data_path = os.path.join(history_path, data_file)

    try:
        # Load daily data from cache or file
        if cache_key not in store_historical_data.daily_data_cache:
            daily_data = []
            if os.path.exists(data_path):
                try:
                    with open(data_path, "r") as file:
                        content = file.read().strip()
                        if content:
                            daily_data = json.loads(content)
                            if not isinstance(daily_data, list):
                                daily_data = []
                except json.JSONDecodeError:
                    print("Daily data file corrupted, attempting repair...")
                    daily_data = repair_json_file(data_path, expect_list=True)

            # Ensure daily_data is a list
            if not isinstance(daily_data, list):
                daily_data = []

            store_historical_data.daily_data_cache[cache_key] = daily_data

        # Sanitize input data
        sanitized_io_dict = sanitize_dict(io_dict)

        # Skip if no valid data or it's a duplicate of the last entry
        if not sanitized_io_dict:
            return

        # Check for duplicates using timestamp if available
        is_duplicate = False
        if sanitized_io_dict.get('_timestamp_'):
            for existing_entry in store_historical_data.daily_data_cache[cache_key]:
                if existing_entry.get('_timestamp_') == sanitized_io_dict.get('_timestamp_'):
                    is_duplicate = True
                    break

        # Only append if not a duplicate
        if not is_duplicate and (not store_historical_data.daily_data_cache[cache_key] or
                               store_historical_data.daily_data_cache[cache_key][-1] != sanitized_io_dict):
            store_historical_data.daily_data_cache[cache_key].append(sanitized_io_dict)

            # Only write to disk if enough time has passed
            if current_time - store_historical_data.last_write_time.get(cache_key, 0) >= store_historical_data.write_interval:
                # Write daily data atomically
                temp_data_path = data_path + ".tmp"
                try:
                    with open(temp_data_path, "w") as file:
                        json.dump(store_historical_data.daily_data_cache[cache_key], file, separators=(',', ':'))
                    os.replace(temp_data_path, data_path)
                    store_historical_data.last_write_time[cache_key] = current_time
                except Exception as e:
                    print(f"Error writing daily data file: {e}")
                    if os.path.exists(temp_data_path):
                        os.remove(temp_data_path)

    except Exception as e:
        print(f"Error in store_historical_data: {e}")

    # Clean up old cache entries to prevent memory leaks
    # Keep only entries from the last 2 days
    current_time = time.time()
    for key in list(store_historical_data.last_write_time.keys()):
        if current_time - store_historical_data.last_write_time[key] > 172800:  # 48 hours
            if key in store_historical_data.daily_data_cache:
                del store_historical_data.daily_data_cache[key]
            del store_historical_data.last_write_time[key]


# Logging device events :)
# Define the Laravel API endpoint
LARAVEL_EVENT_API_URL = PLATFORM_BASE_URL + "api/log-device-event"

def send_event_to_api(imei, io_data):
    """
    Sends event data to the Laravel API.
    :param imei: Device IMEI
    :param io_data: Parsed IO data
    """
    try:
        payload = {
            "imei": imei,
            "data": io_data
        }
        headers = {"Content-Type": "application/json"}
        response = requests.post(LARAVEL_EVENT_API_URL, data=json.dumps(payload), headers=headers)

        if response.status_code == 200:
            print("Event successfully sent to Laravel API.")
        else:
            print(f"Failed to send event. Status Code: {response.status_code}, Response: {response.text}")
    except Exception as e:
        print(f"Error sending event to Laravel API: {e}")


# geofence checker
def is_point_in_geofence(current_point, geofence_data):
    """
    Check if the given point is inside the geofence.
    Supports polygon, circle, and rectangle geofences.
    """
    if geofence_data["type"] == "polygon":
        # Extract polygon coordinates and create Polygon object
        polygon_coords = [(g["lng"], g["lat"]) for g in geofence_data["geofence"]]
        polygon = Polygon(polygon_coords)
        return polygon.contains(current_point)

    elif geofence_data["type"] == "circle":
        # Extract circle center and radius
        center_lat = geofence_data["geofence"]["lat"]
        center_lon = geofence_data["geofence"]["lng"]
        radius = geofence_data["radius"]

        center_point = (center_lat, center_lon)
        distance = geodesic((current_point.y, current_point.x), center_point).meters
        return distance <= radius

    elif geofence_data["type"] == "rectangle":
        # Extract rectangle bounds
        south = geofence_data["geofence"]["south"]
        west = geofence_data["geofence"]["west"]
        north = geofence_data["geofence"]["north"]
        east = geofence_data["geofence"]["east"]

        return (south <= current_point.y <= north) and (west <= current_point.x <= east)

    return False  # Invalid geofence type


def update_geofence_state(imei, new_state):
    print("Getting old geofence")
    file_path = os.path.join(BASE_PATH, "geofence_states/states.json")

    # Ensure the directory exists
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # Ensure the file exists
    if not os.path.exists(file_path):
        with open(file_path, "w") as file:
            json.dump({}, file)

    try:
        with open(file_path, "r") as file:
            content = file.read()
            geofence_data = json.loads(content) if content.strip() else {}
    except (FileNotFoundError, json.JSONDecodeError):
        print("Invalid JSON or file missing. Reinitializing.")
        geofence_data = {}

    # Get the previous state, default to "out"
    previous_state = geofence_data.get(imei, {}).get("state", "out")

    # Always update the geofence data
    geofence_data[imei] = {"state": new_state}

    # Write the updated state back to the file
    with open(file_path, "w") as file:
        json.dump(geofence_data, file, indent=4)

    return previous_state, new_state

def log_geofence_event_to_backend(imei, event_data):
    """Send geofence exit event to Laravel backend."""
    api_url = PLATFORM_BASE_URL + "api/log-geofence-event"
    payload = {"imei": imei, **event_data}
    response = requests.post(api_url, json=payload)
    print(f"Logged event for IMEI {imei}, Response: {response.status_code}")

####################################################
###############____TIME_FUNCTIONS____###############
####################################################

def time_stamper():
	current_server_time = datetime.datetime.now()
	server_time_stamp = current_server_time.strftime('%H:%M:%S %d-%m-%Y')
	return server_time_stamp

def device_time_stamper(timestamp):
    # Convert the hex timestamp to milliseconds and then to seconds
    timestamp_ms = int(timestamp, 16) / 1000

    # Use timezone-aware datetime for UTC
    timestamp_utc = datetime.datetime.fromtimestamp(timestamp_ms, datetime.timezone.utc)

    # Define the timezone for Italy
    italy_timezone = pytz.timezone('Europe/Rome')

    # Convert UTC to Italy timezone
    timestamp_italy = timestamp_utc.astimezone(italy_timezone)

    # Format both timestamps
    formatted_timestamp_italy = timestamp_italy.strftime("%d/%m/%Y %H:%M")  # Italian format

    # Combine both formatted timestamps
    formatted_timestamp = f"{formatted_timestamp_italy}"

    return formatted_timestamp

def time_stamper_for_json():
    # Define the timezone for Italy
    italy_timezone = pytz.timezone('Europe/Rome')

    # Get the current time in Italy's timezone
    current_italy_time = datetime.datetime.now(italy_timezone)

    # Format the time in the desired Italian format
    italy_time_stamp = current_italy_time.strftime('%d/%m/%Y %H:%M')

    return italy_time_stamp


def record_delay_counter(timestamp):
	timestamp_ms = int(timestamp, 16) / 1000
	current_server_time = datetime.datetime.now().timestamp()
	return f"{int(current_server_time - timestamp_ms)} seconds"

####################################################
###############_PARSE_FUNCTIONS_CODE_###############
####################################################

def parse_data_integer(data):
	return int(data, 16)

def int_multiply_01(data):
	return float(decimal.Decimal(int(data, 16)) * decimal.Decimal('0.1'))

def int_multiply_001(data):
	return float(decimal.Decimal(int(data, 16)) * decimal.Decimal('0.01'))

def int_multiply_0001(data):
	return float(decimal.Decimal(int(data, 16)) * decimal.Decimal('0.001'))

def signed_no_multiply(data): #need more testing of this function
	try:
		binary = bytes.fromhex(data.zfill(8))
		value = struct.unpack(">i", binary)[0]
		return value
	except Exception as e:
		print(f"unexpected value received in function '{data}' error: '{e}' will leave unparsed value!")
		return f"0x{data}"

def parse_ibutton(data):
    """
    Special parser for iButton ID (parameter 78)
    Returns the iButton ID as a hex string without '0x' prefix
    """
    try:
        # Return the raw hex string without '0x' prefix
        return data.zfill(16)  # Ensure 16 characters (8 bytes)
    except Exception as e:
        print(f"Error parsing iButton ID: {e}")
        return f"0x{data}"

parse_functions_dictionary = { #this must simply be updated with new AVL IDs and their functions

	240: parse_data_integer,
	239: parse_data_integer,
	80: parse_data_integer,
	21: parse_data_integer,
	200: parse_data_integer,
	69: parse_data_integer,
	181: int_multiply_01,
	182: int_multiply_01,
	66: int_multiply_0001,
	24: parse_data_integer,
	205: parse_data_integer,
	206: parse_data_integer,
	67: int_multiply_0001,
	68: int_multiply_0001,
	241: parse_data_integer,
	299: parse_data_integer,
	16: parse_data_integer,
	1: parse_data_integer,
	9: parse_data_integer,
	179: parse_data_integer,
	12: int_multiply_0001,
	13: int_multiply_001,
	17: signed_no_multiply,
	18: signed_no_multiply,
	19: signed_no_multiply,
	11: parse_data_integer,
	10: parse_data_integer,
	2: parse_data_integer,
	3: parse_data_integer,
	6: int_multiply_0001,
	180: parse_data_integer,
	113: parse_data_integer,
	48: parse_data_integer,
	246: parse_data_integer,
	247: parse_data_integer,
	252: parse_data_integer,
	318: parse_data_integer,
	255: parse_data_integer,
	249: parse_data_integer,
	199: parse_data_integer,
	250: parse_data_integer,
	89: parse_data_integer,
	84: int_multiply_01,
	37: parse_data_integer,
	34: int_multiply_01,
	87: parse_data_integer,
	135: parse_data_integer,
	110: int_multiply_01,
	60: int_multiply_001,
	33: int_multiply_01,
	216: parse_data_integer,
	83: int_multiply_01,
    192: parse_data_integer,
    193: parse_data_integer,
    191: parse_data_integer,
    183: signed_no_multiply,
    1148: signed_no_multiply,
    78: parse_ibutton,
    86: parse_data_integer,
    201: parse_data_integer,

}

def sorting_hat(key, value):
	if key in parse_functions_dictionary:
		parse_function = parse_functions_dictionary[key]
		return parse_function(value)
	else:
		return f"0x{value}"

# Convert degrees to radians
def haversine_distance(lat1, lon1, lat2, lon2):
    R = 6371000  # Radius of Earth in meters
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat / 2)**2 + cos(lat1) * cos(lat2) * sin(dlon / 2)**2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))

    return R * c

# Get current timestamp in Italy Timezone
def get_italy_time():
    italy_tz = pytz.timezone("Europe/Rome")
    return datetime.datetime.now(italy_tz).strftime("%Y-%m-%d %H:%M:%S")

# Function to update route progress
def update_route_progress(imei, latitude, longitude, io_dict):
    date = datetime.datetime.now().strftime("%Y-%m-%d")
    file_path = f"{ROUTE_FOLDER}/{imei}/{date}.json"

    if not os.path.exists(file_path):
        print(f"No route file found for IMEI: {imei} on {date}")
        return

    try:
        # Load route data from JSON file
        with open(file_path, "r") as file:
            route_data = json.load(file)

        # If route is already completed, skip
        if route_data.get("status") == "completed":
            return

        # If route hasn't started yet and we're not at start point, skip
        if (route_data.get("status") == "pending" and
            route_data["start_point"]["status"] == "pending"):
            distance_to_start = haversine_distance(
                float(latitude), float(longitude),
                float(route_data["start_point"]["latitude"]),
                float(route_data["start_point"]["longitude"])
            )
            if distance_to_start > RADIUS_THRESHOLD:
                return

        route_id = route_data["route_id"]
        payload = {
            "route_id": route_id,
            "io_data": io_dict,
            "timestamp": get_italy_time()
        }

        # Check if device is at Start Point
        start_point = route_data["start_point"]
        if start_point["status"] == "pending":
            distance = haversine_distance(
                float(latitude), float(longitude),
                float(start_point["latitude"]),
                float(start_point["longitude"])
            )
            if distance <= RADIUS_THRESHOLD:
                print("Vehicle reached start point, marking as started...")
                payload["type"] = "start_point"
                response = requests.post(UPDATE_ROUTE_API_URL, json=payload)
                if response.status_code == 200:
                    start_point["status"] = "completed"
                    route_data["status"] = "ongoing"

        # Only process stops if route has started
        if route_data["status"] == "ongoing":
            # Check stops in sequential order
            pending_stops = [stop for stop in route_data["stops"] if stop["status"] == "pending"]
            if pending_stops:
                current_stop = pending_stops[0]  # Get the first pending stop
                distance = haversine_distance(
                    float(latitude), float(longitude),
                    float(current_stop["latitude"]),
                    float(current_stop["longitude"])
                )
                if distance <= RADIUS_THRESHOLD:
                    print(f"Vehicle reached stop {current_stop['id']}, marking as completed...")
                    payload["type"] = "stop_point"
                    payload["stop_id"] = current_stop["id"]
                    response = requests.post(UPDATE_ROUTE_API_URL, json=payload)
                    if response.status_code == 200:
                        current_stop["status"] = "completed"

            # Check end point only if all stops are completed
            if all(stop["status"] == "completed" for stop in route_data["stops"]):
                end_point = route_data["end_point"]
                if end_point["status"] == "pending":
                    distance = haversine_distance(
                        float(latitude), float(longitude),
                        float(end_point["latitude"]),
                        float(end_point["longitude"])
                    )
                    if distance <= RADIUS_THRESHOLD:
                        print("Vehicle reached end point, marking as completed...")
                        payload["type"] = "end_point"
                        response = requests.post(UPDATE_ROUTE_API_URL, json=payload)
                        if response.status_code == 200:
                            end_point["status"] = "completed"

        # Check if route is fully completed
        all_stops_completed = all(stop["status"] == "completed" for stop in route_data["stops"])
        if (start_point["status"] == "completed" and
            all_stops_completed and
            route_data["end_point"]["status"] == "completed"):

            print("Route completed, calling complete-route API...")
            complete_payload = {
                "route_id": route_id,
                "timestamp": get_italy_time()
            }
            response = requests.post(COMPLETE_ROUTE_API, json=complete_payload)
            if response.status_code == 200:
                route_data["status"] = "completed"
                # Delete the file after completion
                os.remove(file_path)

        # Save updated JSON
        with open(file_path, "w") as file:
            json.dump(route_data, file, indent=4)

    except Exception as e:
        print(f"Error updating route progress: {e}")


####################################################

def fileAccessTest(): #check if script can create files and folders
	try:
		testDict = {}
		testDict["_Writing_Test_"] = "Writing_Test"
		testDict["Script_Started"] = time_stamper_for_json()

		# json_printer(testDict, "file_Write_Test")

		print (f"---### File access test passed! ###---")
		input_trigger()

	except Exception as e:
		print ()
		print (f"---### File access error occured ###---")
		print (f"'{e}'")
		print (f"---### Try running terminal with Administrator rights! ###---")
		print (f"---### Nothing will be saved if you decide to continue! ###---")
		print ()
		input_trigger()


def main():
	fileAccessTest()

if __name__ == "__main__":
	main()
