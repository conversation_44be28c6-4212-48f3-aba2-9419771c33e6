<?php

use App\Http\Middleware\DashboardMiddleware;
use App\Http\Middleware\SetLocale;
use App\Livewire\Auth\ForgotPassword;
use App\Livewire\Auth\Login;
use App\Livewire\Panel\Alarms;
use App\Livewire\Panel\Dashboard;
use App\Livewire\Panel\VehicleRoutes;
use App\Livewire\Panel\Drivers;
use App\Livewire\Panel\FleetManagement;
use App\Livewire\Panel\Geofencing;
use App\Livewire\Panel\RemoteControl;
use App\Livewire\Panel\Reporting;
use App\Livewire\Panel\Roles;
use App\Livewire\Panel\Settings;
use App\Livewire\Panel\Users;
use App\Livewire\Panel\Vehicles;
use Illuminate\Support\Facades\Route;

// Route::get('test', function () {
//     function getMaxRecDelay($filePath)
//     {
//         // Read and decode JSON file
//         $jsonData = @file_get_contents($filePath);
//         $data = json_decode($jsonData, true);



//         $maxDelay = 0;

//         foreach ($data as $entry) {
//             if (isset($entry['_rec_delay_'])) {
//                 // Extract numeric value from delay string (e.g., "5 seconds" -> 5)
//                 preg_match('/(\d+)\s*seconds?/', $entry['_rec_delay_'], $matches);
//                 if (!empty($matches[1])) {
//                     $delayInSeconds = (int) $matches[1];
//                     $maxDelay = max($maxDelay, $delayInSeconds);
//                 }
//             }
//         }

//         return $maxDelay . " seconds";
//     }

//     // Example usage
//     $filePath = public_path('15-02-2025.json'); // Change this to your actual JSON file path
//     echo getMaxRecDelay($filePath);
// });
Route::middleware(SetLocale::class)->group(function () {
    Route::get('/', Login::class)->name('login');
    Route::get('forgot-password', ForgotPassword::class)->name('forgot-password');
    Route::middleware(DashboardMiddleware::class)->group(function () {
        Route::get('dashboard', Dashboard::class)->name('dashboard');
        Route::get('users', Users::class)->name('users');
        Route::get('vehicles', Vehicles::class)->name('vehicles');
        Route::get('fleet-management', FleetManagement::class)->name('fleet-management');
        Route::get('remote-control', RemoteControl::class)->name('remote-control');
        Route::get('geofencing', Geofencing::class)->name('geofencing');
        Route::get('reporting', Reporting::class)->name('reporting');
        Route::get('drivers', Drivers::class)->name('drivers');
        Route::get('settings', Settings::class)->name('settings');
        Route::get('alarms', Alarms::class)->name('alarms');
        Route::get('roles', Roles::class)->name('roles');
        Route::get('vehicle-routes/{vehicleId}', VehicleRoutes::class)->name('vehicle-routes');

        Route::get('logout', function () {
            auth()->logout();
            return redirect()->route('login');
        })->name('logout');
    });
});


// Route::get('/max-rec-delay', function () {
//     // Read the JSON file from the public path
//     $jsonFile = public_path('data.json');
//     $jsonData = file_get_contents($jsonFile);

//     // Decode the JSON into an array
//     $dataArray = json_decode($jsonData, true);

//     // Initialize max delay variables
//     $maxRecDelay = 0;
//     $maxRecDelayObject = null;

//     // Iterate and find the object with the max _rec_delay_
//     foreach ($dataArray as $item) {
//         // Convert _rec_delay_ to seconds (remove " seconds" and cast to int)
//         $delayValue = intval(str_replace(' seconds', '', $item['_rec_delay_']));

//         if ($delayValue > $maxRecDelay) {
//             $maxRecDelay = $delayValue;
//             $maxRecDelayObject = $item;
//         }
//     }

//     // Return the object as JSON response
//     return response()->json($maxRecDelayObject);
// });
