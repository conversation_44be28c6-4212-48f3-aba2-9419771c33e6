<div class="p-4 text-sm">

    <div class="justify-between md:flex">
        <div>
            <h1 class="text-xl font-medium tracking-wide font-poppins text-slate-800 dark:text-slate-100">
                @lang('translations.reporting')
            </h1>
            <p class="mt-2 text-slate-600 dark:text-slate-300">
                @lang('translations.generate_export_reports')
            </p>
        </div>
        @can('reporting_export')
            <div class="flex items-end justify-end mt-3 h-fit md:mt-0">
                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">

                    <button wire:click="exportToPDF" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                        class="p-2 text-sm text-white transition-all duration-300 bg-red-500 rounded-lg hover:bg-red-600 ">
                        <img src="{{ asset('assets/images/icons/pdf.svg') }}" alt="excel" class="size-5">
                    </button>
                    <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="opacity-0 transform scale-95"
                        x-transition:enter-end="opacity-100 transform scale-100"
                        x-transition:leave="transition ease-in duration-150"
                        x-transition:leave-start="opacity-100 transform scale-100"
                        x-transition:leave-end="opacity-0 transform scale-95"
                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg top-full w-max"
                        style="display: none;">
                        @lang('translations.pdf_export')

                    </div>
                </div>

                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                    <button wire:click="exportToExcel" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                        class="p-2 text-sm text-white transition-all duration-300 rounded-lg ms-2 bg-emerald-500 hover:bg-emerald-600 ">
                        <img src="{{ asset('assets/images/icons/excel.svg') }}" alt="excel" class="size-5">
                    </button>
                    <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="opacity-0 transform scale-95"
                        x-transition:enter-end="opacity-100 transform scale-100"
                        x-transition:leave="transition ease-in duration-150"
                        x-transition:leave-start="opacity-100 transform scale-100"
                        x-transition:leave-end="opacity-0 transform scale-95"
                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg top-full w-max"
                        style="display: none;">
                        @lang('translations.excel_export')
                    </div>
                </div>
            </div>
        @endcan


    </div>


    @can('reporting_view')

        <section class="w-full mx-auto mt-4">

            <div class="grid gap-4 mt-5 md:grid-cols-4 grow">

                <div class="md:col-span-2">
                    <label class="block mb-2.5 dark:text-slate-300">@lang('translations.select_vehicles')</label>
                    <livewire:components.select-dropdown placeholder="{{ __('translations.select_vehicle') }}"
                        field-name="selectedVehicle" fetch-method="getVehicles" />

                </div>
                <div>
                    <label class="dark:text-slate-300">@lang('translations.report_type')</label>
                    <select wire:model.live="reporting_type"
                        class="w-full px-3 py-2 mt-2 border rounded-lg shadow-sm border-slate-200 focus:outline-none focus:border-primary hover:shadow dark:bg-slate-800 dark:text-slate-300 dark:border-slate-500">
                        <option value="routes">@lang('translations.routes')</option>
                        <option value="fuel_consumption">@lang('translations.fuel_consumption')</option>
                        <option value="alarms">@lang('translations.alarms')</option>
                    </select>
                </div>
                <div>
                    <label class="dark:text-slate-300">@lang('translations.date')</label>
                    <input type="date" wire:model="date"
                        class="w-full px-3 py-2 mt-2 border rounded-lg shadow-sm border-slate-200 focus:outline-none focus:border-primary hover:shadow dark:bg-slate-800 dark:text-slate-300 dark:border-slate-500">
                </div>

                @if ($reporting_type == 'alarms')
                    <div class="grid gap-4 md:col-span-4 md:grid-cols-2 grow">
                        <div>
                            <label class="block mb-2.5 dark:text-slate-300">@lang('translations.select_geofence')</label>
                            <livewire:components.multi-select-dropdown
                                placeholder="{{ __('translations.select_geofence') }}" field-name="selectedGeofences"
                                fetch-method="getGeofences" />

                        </div>
                        <div>
                            <label class="dark:text-slate-300">@lang('translations.alarm_type')</label>
                            <select wire:model="alarmType"
                                class="w-full px-3 py-2.5 mt-2 border rounded-lg shadow-sm border-slate-200 focus:outline-none focus:border-primary hover:shadow dark:bg-slate-800 dark:text-slate-300 dark:border-slate-500">
                                <option value="">@lang('translations.select')</option>
                                <option value="geofence_exit">@lang('translations.geofence_out')</option>
                                <option value="geofence_in">@lang('translations.geofence_in')</option>
                                <option value="jamming">@lang('translations.jamming')</option>
                                <option value="towing">@lang('translations.towing')</option>
                                <option value="crash">@lang('translations.crash')</option>
                                <option value="signal_lost">@lang('translations.signal_lost')</option>
                                <option value="unplug">@lang('translations.unplug')</option>
                            </select>
                        </div>
                    </div>
                @endif
            </div>

            <div class="flex items-center justify-center mt-4">
                <button wire:click="generateReport" wire:loading.attr="disabled" wire:target="generateReport"
                    class="flex items-center gap-2 px-4 py-2 text-white transition-all duration-300 rounded-md bg-primary hover:bg-primaryDark">
                    <span wire:loading.remove>
                        @lang('translations.generate_reports')
                    </span>
                    <div wire:loading wire:target="generateReport">
                        <div class="w-6 h-6 dot-spinner">
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                        </div>
                    </div>
                </button>
            </div>



            @if ($reporting_type == 'routes')
                <div class="mt-8 space-y-6">
                    {{-- Summary Card --}}
                    @if (isset($reports['summary']))
                        <div class="p-6 bg-white rounded-lg shadow-lg dark:bg-slate-800">
                            <h2 class="mb-4 text-xl font-semibold text-slate-800 dark:text-slate-200">@lang('translations.data_summary') -
                                {{ $reports['summary']['vehicle'] ?? '' }}
                            </h2>
                            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
                                <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                    <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.travel_duration')</div>
                                    <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                        {{ $reports['summary']['total_trip_duration'] }}
                                    </div>
                                </div>
                                <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                    <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.total_distance')</div>
                                    <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                        {{ $reports['summary']['total_distance'] }}
                                    </div>
                                </div>
                                @if (isset($reports['summary']['total_fuel']) && $reports['summary']['total_fuel'] > 0)
                                    <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                        <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.fuel_consumption')</div>
                                        <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                            {{ $reports['summary']['total_fuel'] }}
                                        </div>
                                    </div>
                                @endif
                                <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                    <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.total_stops')</div>
                                    <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                        {{ $reports['summary']['total_stops'] }}
                                    </div>
                                </div>
                                <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                    <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.stop_duration')</div>
                                    <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                        {{ $reports['summary']['total_stop_duration'] ?? '' }}
                                    </div>
                                </div>
                            </div>

                            <div class="flex flex-col-reverse items-center md:flex-row md:justify-between md:mt-4">
                                <div class="mt-4 space-y-2 md:mt-0 text-slate-400 dark:text-slate-300">

                                    <div class="flex items-center gap-2">
                                        <img class="size-5" src="{{ asset('assets/images/icons/start.svg') }}"
                                            alt="">
                                        {{ $reports['summary']['start_point_address'] ?? '' }}
                                    </div>

                                    <div class="flex items-center gap-2">
                                        <img class="size-5" src="{{ asset('assets/images/icons/end.svg') }}"
                                            alt="">
                                        {{ $reports['summary']['end_point_address'] ?? '' }}
                                    </div>

                                </div>
                                <div class="mt-2 text-sm md:mt-0 text-slate-500 dark:text-slate-400 ms-auto">
                                    @lang('translations.period'): {{ $reports['summary']['start_time'] }} -
                                    {{ $reports['summary']['end_time'] }}
                                </div>
                            </div>

                            {{-- Geofence Events Section --}}
                            @if (isset($reports['summary']['geofence_events']) && count($reports['summary']['geofence_events']) > 0)
                                <div class="pt-4 mt-6 border-t border-slate-200 dark:border-slate-700">
                                    <h3 class="mb-3 text-lg font-semibold text-slate-800 dark:text-slate-200">
                                        @lang('translations.geofence_events')
                                    </h3>
                                    <div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                                        @foreach ($reports['summary']['geofence_events'] as $event)
                                            <div
                                                class="p-3 border rounded-lg bg-slate-50 dark:bg-slate-700 border-slate-200 dark:border-slate-600">
                                                <div class="flex items-center gap-2 mb-2">
                                                    <div
                                                        class="size-2 rounded-full {{ $event['type'] === 'geofence_exit_event' ? 'bg-red-500' : 'bg-green-500' }}">
                                                    </div>
                                                    <span class="text-sm font-medium text-slate-700 dark:text-slate-200">
                                                        {{ $event['type'] === 'geofence_exit_event' ? __('translations.geofence_exit_event', ['geofence' => $event['geofence']]) : __('translations.geofence_entry_event', ['geofence' => $event['geofence']]) }}
                                                    </span>
                                                    <span class="ml-auto text-sm text-slate-500 dark:text-slate-400">
                                                        {{ $event['time'] }}
                                                    </span>
                                                </div>
                                                <div class="space-y-1">
                                                    <div class="text-sm text-slate-600 dark:text-slate-300">
                                                        <span class="font-medium">@lang('translations.geofence'):</span>
                                                        {{ $event['geofence'] }}
                                                    </div>
                                                    <div class="text-sm text-slate-600 dark:text-slate-300">
                                                        <span class="font-medium">@lang('translations.location'):</span>
                                                        {{ $event['location'] }}
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endif

                    {{-- Timeline --}}
                    @if (isset($reports['details']) && count($reports['details']) > 0)
                        <div class="p-6 bg-white rounded-lg shadow-lg dark:bg-slate-800">
                            <h2 class="mb-4 text-xl font-semibold text-slate-800 dark:text-slate-200">@lang('translations.timeline')
                            </h2>
                            <div>
                                @forelse (isset($reports['details']) ? $reports['details'] : [] as $stop)
                                    <div class="flex w-full gap-3">
                                        <div
                                            class="flex flex-col items-center justify-between flex-shrink-0 w-10 h-full text-xs font-medium md:justify-center md:flex-row md:w-28 text-slate-600 dark:text-slate-300 md:text-sm">
                                            {{ $stop['start_time'] }}<br class="md:hidden"> <span
                                                class="hidden md:block">-</span> {{ $stop['end_time'] }}</div>
                                        <div
                                            class="relative w-full pl-8 border-l-2 {{ $stop['type'] == 'trip' ? 'border-green-500' : 'border-primary' }}">
                                            <div
                                                class="absolute w-4 h-4 rounded-full -left-2 {{ $stop['type'] == 'trip' ? 'bg-green-500' : 'bg-primary' }}">
                                            </div>
                                            <div class="pb-4">
                                                <div
                                                    class="p-4 rounded-lg {{ $stop['type'] == 'trip' ? 'bg-green-50 dark:bg-green-900/20' : 'bg-slate-50 dark:bg-slate-700' }}">
                                                    <div
                                                        class="flex flex-col gap-1 mb-2 md:justify-between md:items-center md:flex-row">
                                                        @if ($stop['type'] == 'trip')
                                                            <div class="text-slate-700 dark:text-slate-200">
                                                                <div class="flex items-center gap-2">
                                                                    <span
                                                                        class="font-medium text-green-600 dark:text-green-400">@lang('translations.trip')</span>
                                                                </div>
                                                                <div class="flex items-center gap-2 mt-2">
                                                                    <img class="size-5"
                                                                        src="{{ asset('assets/images/icons/start.svg') }}"
                                                                        alt="">
                                                                    {{ $stop['start_location'] }}
                                                                </div>
                                                                <div class="flex items-center gap-2 mt-1">
                                                                    <img class="size-5"
                                                                        src="{{ asset('assets/images/icons/end.svg') }}"
                                                                        alt="">
                                                                    {{ $stop['end_location'] }}
                                                                </div>
                                                            </div>
                                                        @else
                                                            <div class="text-slate-700 dark:text-slate-200">
                                                                <div class="flex items-center gap-2">
                                                                    <span
                                                                        class="font-medium text-primary dark:text-primary-light">@lang('translations.stop')</span>
                                                                </div>
                                                                <div class="mt-2">
                                                                    {{ $stop['location'] }}
                                                                </div>
                                                            </div>
                                                        @endif

                                                        <div class="text-sm text-slate-500 dark:text-slate-400">
                                                            @lang('translations.duration'): {{ $stop['duration'] }}
                                                        </div>
                                                    </div>

                                                    @if ($stop['type'] == 'trip')
                                                        <div
                                                            class="flex flex-col gap-2 mt-2 text-sm md:gap-4 md:flex-row text-slate-500 dark:text-slate-400">
                                                            @if (isset($stop['trip_distance']) && isset($stop['trip_distance']))
                                                                <div>@lang('translations.trip_distance'):
                                                                    {{ $stop['trip_distance'] ?? '-' }}
                                                                </div>
                                                            @endif
                                                            @if (isset($stop['trip_fuel']) && isset($stop['trip_fuel']))
                                                                <div>@lang('translations.trip_fuel_consumption'):
                                                                    {{ $stop['trip_fuel'] ?? '-' }}
                                                                </div>
                                                            @endif
                                                        </div>
                                                    @else
                                                        <!-- No distance/fuel consumption shown for stops -->
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                @empty
                                    <div class="p-4 text-center text-slate-500 dark:text-slate-400">
                                        @lang('translations.no_record_found')
                                    </div>
                                @endforelse
                            </div>
                        </div>
                    @endif
                </div>
            @elseif ($reporting_type == 'alarms')
                @if ($alarms?->count() > 0)
                    <div class="w-full mt-4 mb-20 overflow-hidden bg-white rounded-lg shadow dark:bg-slate-800">
                        <div class="w-full mb-5 overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr
                                        class="text-xs font-semibold tracking-wide text-left text-gray-600 uppercase bg-gray-100 dark:bg-slate-700 dark:text-slate-300">
                                        <th class="px-4 py-3 whitespace-nowrap">@lang('translations.vehicle')</th>
                                        <th class="px-4 py-3 whitespace-nowrap">@lang('translations.alarm_type')</th>
                                        <th class="px-4 py-3 whitespace-nowrap">@lang('translations.alarm')</th>
                                        <th class="px-4 py-3 whitespace-nowrap">@lang('translations.location')</th>
                                        <th class="px-4 py-3 whitespace-nowrap">@lang('translations.occurred_at')</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-slate-800 dark:text-slate-300">
                                    @forelse ($alarms as $alarm)
                                        <tr>
                                            <td class="px-4 py-2">{{ $alarm->vehicle?->license_plate ?? $alarm->imei }}
                                            </td>
                                            <td class="px-4 py-2">
                                                @if ($alarm->alarm_type == 'jamming')
                                                    {{ __('translations.jamming') }}
                                                @elseif ($alarm->alarm_type == 'towing')
                                                    {{ __('translations.towing') }}
                                                @elseif ($alarm->alarm_type == 'crash')
                                                    {{ __('translations.crash') }}
                                                @elseif ($alarm->alarm_type == 'signal_lost')
                                                    {{ __('translations.signal_lost') }}
                                                @elseif ($alarm->alarm_type == 'unplug')
                                                    {{ __('translations.unplug') }}
                                                @elseif ($alarm->alarm_type == 'geofence_exit')
                                                    {{ __('translations.geofence_exit') }}
                                                @elseif ($alarm->alarm_type == 'geofence_in')
                                                    {{ __('translations.geofence_in') }}
                                                @endif
                                            </td>

                                            <td class="px-4 py-2">
                                                @if ($alarm->alarm_type == 'jamming')
                                                    {{ __('translations.jamming_' . $alarm->alarm_value) }}
                                                @elseif ($alarm->alarm_type == 'towing')
                                                    {{ __('translations.towing_' . $alarm->alarm_value) }}
                                                @elseif ($alarm->alarm_type == 'crash')
                                                    {{ __('translations.crash_' . $alarm->alarm_value) }}
                                                @elseif ($alarm->alarm_type == 'signal_lost')
                                                    {{ __('translations.signal_lost_' . $alarm->alarm_value) }}
                                                @elseif ($alarm->alarm_type == 'unplug')
                                                    {{ __('translations.unplug_' . $alarm->alarm_value) }}
                                                @elseif ($alarm->alarm_type == 'geofence_exit')
                                                    {{ __('translations.geofence_exit_event', ['geofence' => $alarm->geofence?->name]) }}
                                                @elseif ($alarm->alarm_type == 'geofence_in')
                                                    {{ __('translations.geofence_entry', ['geofence' => $alarm->geofence?->name]) }}
                                                @else
                                                    {{ __('translations.unknown_event') }}
                                                @endif
                                            </td>

                                            <td class="px-4 py-2">{{ $alarm->location ?? 'N/A' }}</td>
                                            <td class="px-4 py-2">{{ $alarm->created_at->format('d/m/Y H:i') }}</td>

                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="10" class="px-4 py-3 text-center">@lang('translations.no_record_found')
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>

                        </div>
                        <div class="py-5">
                            {{ $alarms->links('livewire.components.pagination') }}
                        </div>

                    </div>
                @endif
            @elseif($reporting_type == 'fuel_consumption')
                <div class="mt-8 space-y-6">
                    @if (isset($fuelReports['summary']))
                        {{-- Summary Card --}}
                        <div class="p-6 bg-white rounded-lg shadow-lg dark:bg-slate-800">
                            <div class="flex items-center justify-between mb-4">
                                <h2 class="text-xl font-semibold text-slate-800 dark:text-slate-200">
                                    @lang('translations.fuel_report_summary') - {{ $fuelReports['summary']['vehicle'] }}
                                </h2>
                                <div class="text-sm text-slate-600 dark:text-slate-400">
                                    {{ $fuelReports['summary']['date'] }}
                                    @if (isset($fuelReports['summary']['start_time']))
                                        <span class="mx-2">|</span>
                                        {{ $fuelReports['summary']['start_time'] }} -
                                        {{ $fuelReports['summary']['end_time'] }}
                                    @endif
                                </div>
                            </div>

                            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                                @if (isset($fuelReports['summary']['total_fuel_used']) && $fuelReports['summary']['total_fuel_used'] > 0)
                                    {{-- Total Fuel Used --}}
                                    <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                        <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.total_fuel_used')</div>
                                        <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                            {{ $fuelReports['summary']['total_fuel_used'] }} L
                                        </div>
                                    </div>
                                @endif

                                {{-- Total Distance --}}
                                <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                    <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.total_distance')</div>
                                    <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                        {{ $fuelReports['summary']['total_distance'] }} km
                                    </div>
                                </div>

                                {{-- Total Trips --}}
                                <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                    <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.total_trips')</div>
                                    <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                        {{ $fuelReports['summary']['total_trips'] }}
                                    </div>
                                </div>

                                <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                    <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.total_stops')</div>
                                    <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                        {{ $fuelReports['summary']['total_stops'] }}
                                    </div>
                                </div>

                                <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                    <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.travel_duration')</div>
                                    <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                        {{ $fuelReports['summary']['total_trip_duration'] }}
                                    </div>
                                </div>

                                <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                    <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.stop_duration')</div>
                                    <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                        {{ $fuelReports['summary']['total_stop_duration'] }}
                                    </div>
                                </div>

                                {{-- Average Consumption --}}
                                @if (isset($fuelReports['summary']['total_fuel_used']) && $fuelReports['summary']['total_fuel_used'] > 0)
                                    <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                        <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.average_consumption')</div>
                                        <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                            @if ($fuelReports['summary']['total_distance'] > 0)
                                                {{ round(($fuelReports['summary']['total_fuel_used'] / $fuelReports['summary']['total_distance']) * 100, 3) }}
                                                L/100km
                                            @else
                                                0 L/100km
                                            @endif
                                        </div>
                                    </div>
                                @endif

                                @if (isset($fuelReports['summary']['initial_fuel_level']) && $fuelReports['summary']['initial_fuel_level'] > 0)
                                    <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                        <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.initial_fuel_used')</div>
                                        <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                            {{ $fuelReports['summary']['initial_fuel_level'] }} L
                                        </div>
                                    </div>
                                @endif

                                @if (isset($fuelReports['summary']['final_fuel_level']) && $fuelReports['summary']['final_fuel_level'] > 0)
                                    <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                        <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.final_fuel_used')</div>
                                        <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                            {{ $fuelReports['summary']['final_fuel_level'] }} L
                                        </div>
                                    </div>
                                @endif
                                {{-- @if (isset($fuelReports['summary']['refueling_amount']))
                                    <div class="p-4 rounded-lg bg-slate-50 dark:bg-slate-700">
                                        <div class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.refueling_amount')</div>
                                        <div class="text-lg font-semibold text-slate-700 dark:text-slate-200">
                                            {{ $fuelReports['summary']['refueling_amount'] }} L
                                        </div>
                                    </div>
                                @endif --}}
                            </div>
                        </div>

                        {{-- Timeline --}}
                        <div class="p-6 bg-white rounded-lg shadow-lg dark:bg-slate-800">
                            <h2 class="mb-4 text-xl font-semibold text-slate-800 dark:text-slate-200">@lang('translations.timeline')
                            </h2>
                            <div>
                                @forelse ($fuelReports['details'] as $detail)
                                    <div class="flex w-full gap-3">
                                        <div
                                            class="flex flex-col items-center justify-between flex-shrink-0 w-10 h-full text-xs font-medium md:justify-center md:flex-row md:w-28 text-slate-600 dark:text-slate-300 md:text-sm">
                                            {{ $detail['start_time'] }}<br class="md:hidden"> <span
                                                class="hidden md:block">-</span> {{ $detail['end_time'] }}</div>
                                        <div
                                            class="relative w-full pl-8 border-l-2 {{ $detail['type'] == 'trip' ? 'border-green-500' : 'border-primary' }}">
                                            <div
                                                class="absolute w-4 h-4 rounded-full -left-2 {{ $detail['type'] == 'trip' ? 'bg-green-500' : 'bg-primary' }}">
                                            </div>
                                            <div class="pb-4">
                                                <div
                                                    class="p-4 rounded-lg {{ $detail['type'] == 'trip' ? 'bg-green-50 dark:bg-green-900/20' : 'bg-slate-50 dark:bg-slate-700' }}">
                                                    <div
                                                        class="flex flex-col gap-1 mb-2 md:justify-between md:items-center md:flex-row">
                                                        @if ($detail['type'] == 'trip')
                                                            <div class="text-slate-700 dark:text-slate-200">
                                                                <div class="flex items-center gap-2">
                                                                    <span
                                                                        class="font-medium text-green-600 dark:text-green-400">@lang('translations.trip')</span>
                                                                </div>
                                                                <div class="flex items-center gap-2 mt-2">
                                                                    <img class="size-5"
                                                                        src="{{ asset('assets/images/icons/start.svg') }}"
                                                                        alt="">
                                                                    {{ $detail['start_location'] }}
                                                                </div>
                                                                <div class="flex items-center gap-2 mt-1">
                                                                    <img class="size-5"
                                                                        src="{{ asset('assets/images/icons/end.svg') }}"
                                                                        alt="">
                                                                    {{ $detail['end_location'] }}
                                                                </div>
                                                            </div>
                                                        @else
                                                            <div class="text-slate-700 dark:text-slate-200">
                                                                <div class="flex items-center gap-2">
                                                                    <span
                                                                        class="font-medium text-primary dark:text-primary-light">@lang('translations.stop')</span>
                                                                </div>
                                                                <div class="mt-2">
                                                                    {{ $detail['location'] }}
                                                                </div>
                                                            </div>
                                                        @endif

                                                        <div class="text-sm text-slate-500 dark:text-slate-400">
                                                            @lang('translations.duration'): {{ $detail['duration'] }}
                                                        </div>
                                                    </div>

                                                    @if ($detail['type'] == 'trip')
                                                        <div
                                                            class="flex flex-col gap-2 mt-2 text-sm md:gap-4 md:flex-row text-slate-500 dark:text-slate-400">
                                                            @if (isset($detail['trip_distance']) && $detail['trip_distance'])
                                                                <div>@lang('translations.trip_distance'): {{ $detail['trip_distance'] }}
                                                                </div>
                                                            @endif
                                                            @if (isset($detail['trip_fuel']) && $detail['trip_fuel'])
                                                                <div>@lang('translations.trip_fuel_consumption'): {{ $detail['trip_fuel'] }}
                                                                </div>
                                                            @endif
                                                        </div>
                                                    @else
                                                        <div
                                                            class="flex flex-col gap-2 mt-2 text-sm md:gap-4 md:flex-row text-slate-500 dark:text-slate-400">
                                                            <div>@lang('translations.total_odometer'): {{ $detail['odometer'] }}</div>
                                                            <div>@lang('translations.total_fuel_used'): {{ $detail['fuel'] }}</div>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                @empty
                                    <div class="p-4 text-center text-slate-500 dark:text-slate-400">
                                        @lang('translations.no_record_found')
                                    </div>
                                @endforelse
                            </div>
                        </div>
                    @endif
                </div>
            @endif
        </section>

    @endcan


</div>
