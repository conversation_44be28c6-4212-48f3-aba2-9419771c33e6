<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // create permissions
        $permissions = [
            'dashboard_view',
            'user_view',
            'user_add',
            'user_edit',
            'user_delete',
            'user_vehicle_assignment',
            'vehicle_view',
            'vehicle_add',
            'vehicle_edit',
            'vehicle_delete',
            'vehicle_events_management',
            'vehicle_events_view',
            'all_vehicle_events_view',
            'vehicle_route_management',
            'vehicle_users',
            'driver_view',
            'driver_add',
            'driver_edit',
            'driver_delete',
            'fleet_view',
            'all_vehicles_access',
            'fleet_view_history_mode',
            'remote_control',
            'remote_control_pin_manage',
            'geofence_view',
            'geofence_add',
            'geofence_edit',
            'geofence_delete',
            'geofence_vehicle_assignment',
            'reporting_view',
            'reporting_export',
            'notifications_view',
            'roles_management',
            'add_user_role',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['guard_name' => 'web', 'name' => $permission]);
        }

        // create roles and assign created permissions
        $role = Role::firstOrCreate(['guard_name' => 'web', 'name' => 'Admin']);
        $role->givePermissionTo(Permission::all());

        $user = User::find(2);

        $user->assignRole($role);
    }
}
